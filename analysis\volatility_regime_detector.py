"""
Volatility Regime Detection System
The Architect's implementation - know when the market is changing before it kills you
"""
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class VolatilityRegimeDetector:
    """
    Volatility regime detection and transition monitoring
    The Architect: This tells you when to stop trading before regime changes destroy you
    """
    
    def __init__(self):
        self.regime_lookback = 252  # 1 year lookback for regime analysis
        self.transition_threshold = 0.7  # Confidence threshold for regime transitions
        
        # Regime characteristics
        self.regime_definitions = {
            "LOW_VOL": {"vix_range": (8, 15), "realized_vol_range": (0.08, 0.15)},
            "NORMAL_VOL": {"vix_range": (15, 25), "realized_vol_range": (0.15, 0.25)},
            "HIGH_VOL": {"vix_range": (25, 40), "realized_vol_range": (0.25, 0.40)},
            "CRISIS_VOL": {"vix_range": (40, 80), "realized_vol_range": (0.40, 1.00)}
        }
    
    def detect_current_regime(self, vix_data: pd.DataFrame, 
                            price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Detect current volatility regime
        The Architect: This is your early warning system for regime changes
        """
        if len(vix_data) < 30:
            return {"regime": "UNKNOWN", "confidence": 0.0, "error": "Insufficient data"}
        
        regime_analysis = {
            "current_regime": "UNKNOWN",
            "regime_confidence": 0.0,
            "regime_stability": 0.0,
            "transition_probability": 0.0,
            "regime_characteristics": {},
            "trading_recommendation": "HOLD",
            "risk_level": "MEDIUM"
        }
        
        # Get recent data
        recent_data = vix_data.tail(30)  # Last 30 days
        current_vix = recent_data['vix'].iloc[-1]
        
        # Calculate realized volatility if price data available
        realized_vol = None
        if price_data is not None and len(price_data) > 20:
            returns = price_data['close'].pct_change().dropna()
            realized_vol = returns.tail(20).std() * np.sqrt(252)
        
        # Classify current regime based on VIX level
        current_regime = self._classify_regime_by_vix(current_vix)
        regime_analysis["current_regime"] = current_regime
        
        # Calculate regime stability
        regime_analysis["regime_stability"] = self._calculate_regime_stability(recent_data)
        
        # Calculate regime confidence
        regime_analysis["regime_confidence"] = self._calculate_regime_confidence(
            recent_data, current_regime
        )
        
        # Detect regime transition probability
        regime_analysis["transition_probability"] = self._calculate_transition_probability(
            vix_data.tail(60)  # Last 60 days for transition analysis
        )
        
        # Add regime characteristics
        regime_analysis["regime_characteristics"] = self._get_regime_characteristics(
            current_vix, realized_vol, recent_data
        )
        
        # Generate trading recommendation
        regime_analysis["trading_recommendation"] = self._get_trading_recommendation(
            current_regime, regime_analysis["transition_probability"], 
            regime_analysis["regime_stability"]
        )
        
        # Set risk level
        regime_analysis["risk_level"] = self._determine_risk_level(
            current_regime, regime_analysis["transition_probability"]
        )
        
        return regime_analysis
    
    def _classify_regime_by_vix(self, current_vix: float) -> str:
        """Classify regime based on current VIX level"""
        for regime, characteristics in self.regime_definitions.items():
            vix_min, vix_max = characteristics["vix_range"]
            if vix_min <= current_vix < vix_max:
                return regime
        
        # Handle extreme cases
        if current_vix < 8:
            return "LOW_VOL"
        elif current_vix >= 80:
            return "CRISIS_VOL"
        else:
            return "HIGH_VOL"
    
    def _calculate_regime_stability(self, recent_data: pd.DataFrame) -> float:
        """Calculate how stable the current regime is"""
        if len(recent_data) < 10:
            return 0.0
        
        # Calculate VIX volatility (volatility of volatility)
        vix_returns = recent_data['vix'].pct_change().dropna()
        vix_volatility = vix_returns.std()
        
        # Lower VIX volatility = higher regime stability
        # Normalize to 0-1 scale
        stability = max(0, 1 - (vix_volatility / 0.1))  # 10% daily VIX vol = 0 stability
        
        return min(1.0, stability)
    
    def _calculate_regime_confidence(self, recent_data: pd.DataFrame, 
                                   current_regime: str) -> float:
        """Calculate confidence in current regime classification"""
        if len(recent_data) < 5:
            return 0.0
        
        # Check how many recent days fall within current regime
        regime_char = self.regime_definitions.get(current_regime, {})
        vix_range = regime_char.get("vix_range", (0, 100))
        
        days_in_regime = 0
        for vix_value in recent_data['vix'].tail(10):
            if vix_range[0] <= vix_value < vix_range[1]:
                days_in_regime += 1
        
        confidence = days_in_regime / min(10, len(recent_data))
        return confidence
    
    def _calculate_transition_probability(self, extended_data: pd.DataFrame) -> float:
        """Calculate probability of regime transition"""
        if len(extended_data) < 30:
            return 0.0
        
        # Use multiple indicators for transition detection
        transition_signals = []
        
        # 1. VIX trend analysis
        vix_trend = self._analyze_vix_trend(extended_data)
        transition_signals.append(vix_trend)
        
        # 2. Volatility clustering analysis
        clustering_signal = self._analyze_volatility_clustering(extended_data)
        transition_signals.append(clustering_signal)
        
        # 3. Mean reversion breakdown
        mean_reversion_signal = self._analyze_mean_reversion_breakdown(extended_data)
        transition_signals.append(mean_reversion_signal)
        
        # Combine signals
        avg_signal = np.mean(transition_signals)
        return min(1.0, max(0.0, avg_signal))
    
    def _analyze_vix_trend(self, data: pd.DataFrame) -> float:
        """Analyze VIX trend for regime transition signals"""
        if len(data) < 20:
            return 0.0
        
        # Calculate trend strength
        vix_values = data['vix'].values
        x = np.arange(len(vix_values))
        
        # Linear regression to detect trend
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, vix_values)
        
        # Strong trend indicates potential regime change
        trend_strength = abs(r_value)  # R-squared indicates trend strength
        
        # High slope with high R-squared = high transition probability
        if trend_strength > 0.6 and abs(slope) > 0.2:
            return trend_strength
        
        return 0.0
    
    def _analyze_volatility_clustering(self, data: pd.DataFrame) -> float:
        """Analyze volatility clustering patterns"""
        if len(data) < 20:
            return 0.0
        
        # Calculate VIX changes
        vix_changes = data['vix'].diff().abs()
        
        # Look for clustering (high volatility followed by high volatility)
        recent_changes = vix_changes.tail(5).mean()
        historical_changes = vix_changes.head(-5).mean()
        
        if historical_changes > 0:
            clustering_ratio = recent_changes / historical_changes
            
            # High clustering ratio indicates regime instability
            if clustering_ratio > 2.0:
                return min(1.0, (clustering_ratio - 1.0) / 2.0)
        
        return 0.0
    
    def _analyze_mean_reversion_breakdown(self, data: pd.DataFrame) -> float:
        """Analyze breakdown of mean reversion"""
        if len(data) < 30:
            return 0.0
        
        # Calculate how far VIX is from its moving average
        data_copy = data.copy()
        data_copy['vix_ma_20'] = data_copy['vix'].rolling(20).mean()
        data_copy['vix_deviation'] = abs(data_copy['vix'] - data_copy['vix_ma_20'])
        
        # Recent deviation vs historical
        recent_deviation = data_copy['vix_deviation'].tail(5).mean()
        historical_deviation = data_copy['vix_deviation'].head(-5).mean()
        
        if historical_deviation > 0:
            deviation_ratio = recent_deviation / historical_deviation
            
            # High deviation ratio indicates mean reversion breakdown
            if deviation_ratio > 1.5:
                return min(1.0, (deviation_ratio - 1.0) / 1.5)
        
        return 0.0
    
    def _get_regime_characteristics(self, current_vix: float, 
                                  realized_vol: Optional[float],
                                  recent_data: pd.DataFrame) -> Dict[str, Any]:
        """Get detailed characteristics of current regime"""
        characteristics = {
            "current_vix": current_vix,
            "vix_percentile_30d": None,
            "vix_trend_30d": None,
            "volatility_clustering": None,
            "mean_reversion_strength": None
        }
        
        if len(recent_data) >= 30:
            # VIX percentile over last 30 days
            characteristics["vix_percentile_30d"] = (
                recent_data['vix'].rank(pct=True).iloc[-1]
            )
            
            # VIX trend
            vix_30d_change = (recent_data['vix'].iloc[-1] - recent_data['vix'].iloc[0])
            characteristics["vix_trend_30d"] = vix_30d_change
        
        if realized_vol:
            characteristics["realized_volatility"] = realized_vol
            characteristics["vix_rv_spread"] = current_vix - (realized_vol * 100)
        
        return characteristics
    
    def _get_trading_recommendation(self, current_regime: str, 
                                  transition_prob: float,
                                  regime_stability: float) -> str:
        """Get trading recommendation based on regime analysis"""
        
        # High transition probability = avoid trading
        if transition_prob > 0.7:
            return "HALT_TRADING"
        
        # Low regime stability = reduce position sizes
        if regime_stability < 0.3:
            return "REDUCE_POSITIONS"
        
        # Regime-specific recommendations
        if current_regime == "LOW_VOL" and regime_stability > 0.6:
            return "LONG_VOLATILITY_FAVORABLE"
        elif current_regime == "HIGH_VOL" and regime_stability > 0.6:
            return "SHORT_VOLATILITY_FAVORABLE"
        elif current_regime == "CRISIS_VOL":
            return "DEFENSIVE_ONLY"
        else:
            return "NORMAL_TRADING"
    
    def _determine_risk_level(self, current_regime: str, transition_prob: float) -> str:
        """Determine overall risk level"""
        if current_regime == "CRISIS_VOL" or transition_prob > 0.8:
            return "EXTREME"
        elif current_regime == "HIGH_VOL" or transition_prob > 0.6:
            return "HIGH"
        elif transition_prob > 0.4:
            return "ELEVATED"
        elif current_regime == "LOW_VOL":
            return "LOW"
        else:
            return "MEDIUM"
    
    def get_regime_trading_filters(self, regime_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get specific trading filters based on regime analysis
        The Architect: These are your kill switches for different regimes
        """
        filters = {
            "allow_new_positions": True,
            "max_position_size_multiplier": 1.0,
            "required_confidence_threshold": 0.6,
            "force_hedge_positions": False,
            "emergency_exit_trigger": False
        }
        
        current_regime = regime_analysis.get("current_regime", "UNKNOWN")
        transition_prob = regime_analysis.get("transition_probability", 0.0)
        risk_level = regime_analysis.get("risk_level", "MEDIUM")
        
        # Regime-specific filters
        if current_regime == "CRISIS_VOL":
            filters["allow_new_positions"] = False
            filters["emergency_exit_trigger"] = True
            filters["force_hedge_positions"] = True
        
        elif current_regime == "HIGH_VOL":
            filters["max_position_size_multiplier"] = 0.5
            filters["required_confidence_threshold"] = 0.8
            filters["force_hedge_positions"] = True
        
        elif transition_prob > 0.7:
            filters["allow_new_positions"] = False
            filters["max_position_size_multiplier"] = 0.3
        
        elif transition_prob > 0.5:
            filters["max_position_size_multiplier"] = 0.7
            filters["required_confidence_threshold"] = 0.8
        
        # Risk level adjustments
        if risk_level == "EXTREME":
            filters["allow_new_positions"] = False
            filters["emergency_exit_trigger"] = True
        elif risk_level == "HIGH":
            filters["max_position_size_multiplier"] *= 0.5
        
        return filters
