"""
Confidence Scoring Validator
The Architect's Fix #4: Validate Your Confidence Scoring

Take your last 100 signals, rank them by confidence, and measure actual P&L
by confidence bucket. If your 90%+ confidence signals don't significantly
outperform your 60% signals, your scoring is worthless.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from scipy import stats

logger = logging.getLogger(__name__)

@dataclass
class SignalResult:
    """Signal with actual outcome"""
    timestamp: datetime
    symbol: str
    confidence: float
    predicted_direction: int  # 1 for bullish, -1 for bearish
    actual_pnl: float
    actual_direction: int  # 1 if profitable, -1 if loss
    was_correct: bool
    signal_strength: float

@dataclass
class ConfidenceBucket:
    """Performance metrics for a confidence range"""
    confidence_range: Tuple[float, float]
    signal_count: int
    win_rate: float
    avg_pnl: float
    total_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    accuracy: float  # Direction prediction accuracy

@dataclass
class ConfidenceValidationResult:
    """Results of confidence scoring validation"""
    is_valid: bool
    correlation_confidence_pnl: float
    p_value: float
    buckets: List[ConfidenceBucket]
    high_confidence_outperformance: float  # How much 90%+ beats 60%+
    recommendation: str

class ConfidenceScoringValidator:
    """
    Validates confidence scoring system against actual P&L
    Brutal reality check: does higher confidence = higher returns?
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # Validation parameters
        self.min_signals_required = self.config.get('min_signals_required', 100)
        self.confidence_buckets = self.config.get('confidence_buckets', [
            (0.0, 0.6),   # Low confidence
            (0.6, 0.75),  # Medium confidence
            (0.75, 0.85), # High confidence
            (0.85, 0.95), # Very high confidence
            (0.95, 1.0)   # Extreme confidence
        ])

        # Statistical significance thresholds
        self.min_correlation = self.config.get('min_correlation', 0.3)
        self.max_p_value = self.config.get('max_p_value', 0.05)
        self.min_outperformance = self.config.get('min_outperformance', 50.0)  # 50% better

    def validate_confidence_scoring(self, signals: List[SignalResult]) -> ConfidenceValidationResult:
        """
        Validate confidence scoring system

        Args:
            signals: List of signals with actual outcomes

        Returns:
            Validation results with brutal honesty
        """
        if len(signals) < self.min_signals_required:
            return ConfidenceValidationResult(
                is_valid=False,
                correlation_confidence_pnl=0.0,
                p_value=1.0,
                buckets=[],
                high_confidence_outperformance=0.0,
                recommendation=f"Insufficient signals: {len(signals)} < {self.min_signals_required}"
            )

        logger.info(f"Validating confidence scoring with {len(signals)} signals")

        # Calculate correlation between confidence and P&L
        confidences = [s.confidence for s in signals]
        pnls = [s.actual_pnl for s in signals]

        correlation, p_value = stats.pearsonr(confidences, pnls)

        # Analyze performance by confidence buckets
        buckets = self._analyze_confidence_buckets(signals)

        # Calculate high vs medium confidence outperformance
        outperformance = self._calculate_outperformance(buckets)

        # Determine if confidence scoring is valid
        is_valid = (
            abs(correlation) >= self.min_correlation and
            p_value <= self.max_p_value and
            outperformance >= self.min_outperformance
        )

        recommendation = self._generate_recommendation(
            correlation, p_value, outperformance, buckets
        )

        return ConfidenceValidationResult(
            is_valid=is_valid,
            correlation_confidence_pnl=correlation,
            p_value=p_value,
            buckets=buckets,
            high_confidence_outperformance=outperformance,
            recommendation=recommendation
        )

    def _analyze_confidence_buckets(self, signals: List[SignalResult]) -> List[ConfidenceBucket]:
        """
        Analyze performance by confidence buckets

        Args:
            signals: List of signals

        Returns:
            List of confidence bucket analyses
        """
        buckets = []

        for conf_min, conf_max in self.confidence_buckets:
            # Filter signals in this confidence range
            bucket_signals = [
                s for s in signals
                if conf_min <= s.confidence < conf_max
            ]

            if not bucket_signals:
                continue

            # Calculate metrics
            win_rate = np.mean([1 if s.was_correct else 0 for s in bucket_signals])
            avg_pnl = np.mean([s.actual_pnl for s in bucket_signals])
            total_pnl = sum([s.actual_pnl for s in bucket_signals])

            # Calculate Sharpe ratio
            pnls = [s.actual_pnl for s in bucket_signals]
            sharpe_ratio = np.mean(pnls) / np.std(pnls) if np.std(pnls) > 0 else 0

            # Calculate max drawdown
            cumulative_pnl = np.cumsum(pnls)
            running_max = np.maximum.accumulate(cumulative_pnl)
            drawdowns = (running_max - cumulative_pnl) / np.maximum(running_max, 1)
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

            # Direction prediction accuracy
            accuracy = np.mean([
                1 if s.predicted_direction == s.actual_direction else 0
                for s in bucket_signals
            ])

            bucket = ConfidenceBucket(
                confidence_range=(conf_min, conf_max),
                signal_count=len(bucket_signals),
                win_rate=win_rate,
                avg_pnl=avg_pnl,
                total_pnl=total_pnl,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                accuracy=accuracy
            )

            buckets.append(bucket)

        return buckets

    def _calculate_outperformance(self, buckets: List[ConfidenceBucket]) -> float:
        """
        Calculate how much high confidence signals outperform medium confidence

        Args:
            buckets: Confidence bucket analyses

        Returns:
            Outperformance percentage
        """
        # Find high confidence bucket (85%+)
        high_conf_bucket = None
        for bucket in buckets:
            if bucket.confidence_range[0] >= 0.85:
                high_conf_bucket = bucket
                break

        # Find medium confidence bucket (60-75%)
        medium_conf_bucket = None
        for bucket in buckets:
            if 0.6 <= bucket.confidence_range[0] < 0.75:
                medium_conf_bucket = bucket
                break

        if not high_conf_bucket or not medium_conf_bucket:
            return 0.0

        if medium_conf_bucket.avg_pnl <= 0:
            return 0.0

        outperformance = (
            (high_conf_bucket.avg_pnl - medium_conf_bucket.avg_pnl) /
            abs(medium_conf_bucket.avg_pnl) * 100
        )

        return outperformance

    def _generate_recommendation(
        self,
        correlation: float,
        p_value: float,
        outperformance: float,
        buckets: List[ConfidenceBucket]
    ) -> str:
        """
        Generate brutal recommendation based on validation results

        Args:
            correlation: Confidence-PnL correlation
            p_value: Statistical significance
            outperformance: High vs medium confidence outperformance
            buckets: Confidence bucket analyses

        Returns:
            Brutal recommendation
        """
        if abs(correlation) < self.min_correlation:
            return f"CONFIDENCE SCORING IS WORTHLESS: Correlation {correlation:.3f} < {self.min_correlation}. Your confidence has no relationship to actual returns."

        if p_value > self.max_p_value:
            return f"CONFIDENCE SCORING IS NOT SIGNIFICANT: p-value {p_value:.4f} > {self.max_p_value}. Results could be random chance."

        if outperformance < self.min_outperformance:
            return f"HIGH CONFIDENCE SIGNALS DON'T OUTPERFORM: Only {outperformance:.1f}% better than medium confidence. Not worth the complexity."

        # If we get here, confidence scoring is valid
        best_bucket = max(buckets, key=lambda b: b.avg_pnl)
        worst_bucket = min(buckets, key=lambda b: b.avg_pnl)

        return (
            f"CONFIDENCE SCORING IS VALID: "
            f"Correlation {correlation:.3f}, p-value {p_value:.4f}, "
            f"high confidence outperforms by {outperformance:.1f}%. "
            f"Best bucket: {best_bucket.confidence_range} with {best_bucket.avg_pnl:.0f} avg PnL. "
            f"Focus on signals >85% confidence."
        )