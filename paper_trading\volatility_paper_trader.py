"""
Paper Trading System for Volatility Mean Reversion Strategy
The Architect's implementation - realistic execution with proper costs and slippage
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import uuid
import json
import numpy as np

from models.options_greeks import OptionsGreeksCalculator
from utils.cache import cache_manager

logger = logging.getLogger(__name__)

class VolatilityPaperTrader:
    """
    Paper trading system for volatility mean reversion strategy
    The Architect: This models real trading with proper costs and slippage
    """
    
    def __init__(self, initial_capital: float = 1000000):
        self.cache = cache_manager
        self.greeks_calculator = OptionsGreeksCalculator()
        
        # Portfolio state
        self.initial_capital = initial_capital
        self.available_cash = initial_capital
        self.active_positions: Dict[str, Dict] = {}
        self.closed_positions: List[Dict] = []
        
        # Trading costs (realistic for Indian markets)
        self.brokerage_per_lot = 20.0  # ₹20 per lot
        self.stt_rate = 0.0005  # 0.05% STT on options premium
        self.transaction_charges = 0.00053  # NSE transaction charges
        self.gst_rate = 0.18  # 18% GST on brokerage
        
        # Slippage modeling
        self.bid_ask_spread_pct = 0.02  # 2% average spread
        self.market_impact_factor = 0.001  # Additional slippage for larger orders
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_portfolio_value = initial_capital
        
    def calculate_transaction_costs(self, premium: float, lots: int) -> float:
        """
        Calculate realistic transaction costs for Indian options
        The Architect: This is what kills most retail traders
        """
        # Brokerage
        brokerage = lots * self.brokerage_per_lot
        
        # STT on premium
        stt = premium * lots * 75 * self.stt_rate  # 75 = NSE lot size
        
        # Transaction charges
        transaction_charges = premium * lots * 75 * self.transaction_charges
        
        # GST on brokerage
        gst = brokerage * self.gst_rate
        
        total_cost = brokerage + stt + transaction_charges + gst
        
        return round(total_cost, 2)
    
    def apply_slippage(self, theoretical_price: float, side: str, lots: int) -> float:
        """
        Apply realistic slippage based on bid-ask spread and market impact
        The Architect: Paper trading without slippage is fantasy
        """
        # Base slippage from bid-ask spread
        spread_slippage = theoretical_price * self.bid_ask_spread_pct / 2
        
        # Market impact (higher for larger orders)
        market_impact = theoretical_price * self.market_impact_factor * np.log(1 + lots)
        
        total_slippage = spread_slippage + market_impact
        
        # Apply slippage based on side
        if side == "BUY":
            return theoretical_price + total_slippage
        else:  # SELL
            return theoretical_price - total_slippage
    
    async def execute_volatility_trade(self, signal: Dict[str, Any]) -> Optional[Dict]:
        """
        Execute a volatility strategy trade (straddle/strangle)
        The Architect: This is where theory meets reality
        """
        try:
            strategy = signal.get("strategy", "")
            options = signal.get("options", [])
            lots = signal.get("lots", 1)
            
            if not options or lots <= 0:
                logger.warning("Invalid signal: no options or invalid lots")
                return None
            
            # Calculate total premium and costs
            total_premium = 0.0
            executed_legs = []
            
            for option in options:
                # Apply slippage to option price
                side = "SELL" if strategy == "SHORT_VOLATILITY" else "BUY"
                execution_price = self.apply_slippage(option.last_price, side, lots)
                
                leg_premium = execution_price * lots * 75  # NSE lot size
                total_premium += leg_premium
                
                executed_legs.append({
                    "option_type": option.option_type,
                    "strike": option.strike,
                    "expiry": option.expiry_date,
                    "execution_price": execution_price,
                    "theoretical_price": option.last_price,
                    "slippage": execution_price - option.last_price,
                    "premium": leg_premium
                })
            
            # Calculate transaction costs
            transaction_costs = self.calculate_transaction_costs(
                sum(leg["theoretical_price"] for leg in executed_legs) / len(executed_legs), 
                lots * len(executed_legs)
            )
            
            # Check available cash
            if strategy == "LONG_VOLATILITY":
                required_cash = total_premium + transaction_costs
                if required_cash > self.available_cash:
                    logger.warning(f"Insufficient cash: {required_cash} > {self.available_cash}")
                    return None
                
                # Deduct cash for long position
                self.available_cash -= required_cash
            else:
                # For short positions, we receive premium but need margin
                # Simplified: assume 20% margin requirement
                margin_required = total_premium * 0.2 + transaction_costs
                if margin_required > self.available_cash:
                    logger.warning(f"Insufficient margin: {margin_required} > {self.available_cash}")
                    return None
                
                # Block margin, credit premium
                self.available_cash -= margin_required
                self.available_cash += total_premium
            
            # Create position
            position_id = str(uuid.uuid4())
            position = {
                "id": position_id,
                "strategy": strategy,
                "legs": executed_legs,
                "lots": lots,
                "entry_time": datetime.now(),
                "entry_premium": total_premium,
                "transaction_costs": transaction_costs,
                "net_premium": total_premium - transaction_costs if strategy == "SHORT_VOLATILITY" else -(total_premium + transaction_costs),
                "profit_target": signal.get("profit_target", 0),
                "stop_loss": signal.get("stop_loss", 0),
                "confidence": signal.get("confidence", 0),
                "vix_data": signal.get("vix_data", {}),
                "status": "OPEN"
            }
            
            self.active_positions[position_id] = position
            self.total_trades += 1
            
            # Log execution
            logger.info(f"Executed {strategy}: {len(executed_legs)} legs, {lots} lots")
            logger.info(f"Total premium: ₹{total_premium:.2f}, Costs: ₹{transaction_costs:.2f}")
            
            return position
            
        except Exception as e:
            logger.error(f"Failed to execute volatility trade: {e}")
            return None
    
    async def update_positions(self, current_prices: Dict[Tuple, float]):
        """
        Update position values with current market prices
        current_prices: {(strike, expiry, option_type): price}
        """
        try:
            for position_id, position in self.active_positions.items():
                current_value = 0.0
                
                for leg in position["legs"]:
                    key = (leg["strike"], leg["expiry"], leg["option_type"])
                    if key in current_prices:
                        current_price = current_prices[key]
                        leg_value = current_price * position["lots"] * 75
                        current_value += leg_value
                        leg["current_price"] = current_price
                
                # Calculate unrealized P&L
                if position["strategy"] == "LONG_VOLATILITY":
                    unrealized_pnl = current_value - position["entry_premium"]
                else:  # SHORT_VOLATILITY
                    unrealized_pnl = position["entry_premium"] - current_value
                
                # Subtract transaction costs
                unrealized_pnl -= position["transaction_costs"]
                
                position["current_value"] = current_value
                position["unrealized_pnl"] = unrealized_pnl
                
                # Check exit conditions
                await self._check_exit_conditions(position_id, position)
                
        except Exception as e:
            logger.error(f"Failed to update positions: {e}")
    
    async def _check_exit_conditions(self, position_id: str, position: Dict):
        """
        Check if position should be closed based on profit/loss targets
        The Architect: Discipline in exits is what separates pros from amateurs
        """
        try:
            unrealized_pnl = position.get("unrealized_pnl", 0)
            entry_premium = position["entry_premium"]
            
            # Check profit target
            profit_target = position.get("profit_target", 0)
            if profit_target > 0:
                if position["strategy"] == "LONG_VOLATILITY":
                    # 100% gain target
                    if unrealized_pnl >= entry_premium:
                        await self.close_position(position_id, "Profit target reached")
                        return
                else:
                    # 50% of premium target
                    if unrealized_pnl >= entry_premium * 0.5:
                        await self.close_position(position_id, "Profit target reached")
                        return
            
            # Check stop loss
            stop_loss = position.get("stop_loss", 0)
            if stop_loss > 0:
                if position["strategy"] == "LONG_VOLATILITY":
                    # 50% loss limit
                    if unrealized_pnl <= -entry_premium * 0.5:
                        await self.close_position(position_id, "Stop loss hit")
                        return
                else:
                    # 200% of premium loss limit
                    if unrealized_pnl <= -entry_premium * 2.0:
                        await self.close_position(position_id, "Stop loss hit")
                        return
            
            # Check time-based exit (2 days before expiry)
            for leg in position["legs"]:
                expiry_date = leg["expiry"]
                if isinstance(expiry_date, str):
                    expiry_date = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                elif isinstance(expiry_date, datetime):
                    expiry_date = expiry_date.date()

                days_to_expiry = (expiry_date - datetime.now().date()).days
                if days_to_expiry <= 2:
                    await self.close_position(position_id, f"Time exit: {days_to_expiry} days to expiry")
                    return
                    
        except Exception as e:
            logger.error(f"Failed to check exit conditions for {position_id}: {e}")
    
    async def close_position(self, position_id: str, reason: str = "Manual") -> Optional[Dict]:
        """
        Close a position and realize P&L
        The Architect: This is where you find out if you made money
        """
        try:
            if position_id not in self.active_positions:
                logger.warning(f"Position {position_id} not found")
                return None
            
            position = self.active_positions[position_id]
            
            # Calculate exit costs
            avg_price = sum(leg.get("current_price", leg["execution_price"]) 
                          for leg in position["legs"]) / len(position["legs"])
            exit_costs = self.calculate_transaction_costs(avg_price, 
                                                        position["lots"] * len(position["legs"]))
            
            # Calculate final P&L
            final_pnl = position.get("unrealized_pnl", 0) - exit_costs
            
            # Update cash
            if position["strategy"] == "LONG_VOLATILITY":
                # Add current value back to cash
                self.available_cash += position.get("current_value", 0)
            else:
                # Release margin, pay current value
                margin_blocked = position["entry_premium"] * 0.2
                self.available_cash += margin_blocked
                self.available_cash -= position.get("current_value", 0)
            
            # Update position
            position["status"] = "CLOSED"
            position["exit_time"] = datetime.now()
            position["exit_reason"] = reason
            position["exit_costs"] = exit_costs
            position["final_pnl"] = final_pnl
            
            # Update performance metrics
            self.total_pnl += final_pnl
            if final_pnl > 0:
                self.winning_trades += 1
            
            # Update drawdown
            current_portfolio_value = self.get_portfolio_value()
            if current_portfolio_value > self.peak_portfolio_value:
                self.peak_portfolio_value = current_portfolio_value
            else:
                drawdown = (self.peak_portfolio_value - current_portfolio_value) / self.peak_portfolio_value
                self.max_drawdown = max(self.max_drawdown, drawdown)
            
            # Move to closed positions
            self.closed_positions.append(position)
            del self.active_positions[position_id]
            
            logger.info(f"Closed position {position_id}: {reason}, P&L: ₹{final_pnl:.2f}")
            
            return position
            
        except Exception as e:
            logger.error(f"Failed to close position {position_id}: {e}")
            return None
    
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value"""
        total_value = self.available_cash
        
        for position in self.active_positions.values():
            if position["strategy"] == "LONG_VOLATILITY":
                total_value += position.get("current_value", 0)
            else:
                # For short positions, value is already reflected in cash
                pass
        
        return total_value
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive performance summary
        The Architect: These are the numbers that matter
        """
        portfolio_value = self.get_portfolio_value()
        total_return = (portfolio_value - self.initial_capital) / self.initial_capital
        
        win_rate = self.winning_trades / max(1, len(self.closed_positions))
        
        # Calculate Sharpe ratio (simplified)
        if len(self.closed_positions) > 1:
            returns = [pos["final_pnl"] / self.initial_capital for pos in self.closed_positions]
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = avg_return / max(0.001, std_return) * np.sqrt(252)  # Annualized
        else:
            sharpe_ratio = 0.0
        
        return {
            "initial_capital": self.initial_capital,
            "current_portfolio_value": round(portfolio_value, 2),
            "available_cash": round(self.available_cash, 2),
            "total_pnl": round(self.total_pnl, 2),
            "total_return_pct": round(total_return * 100, 2),
            "max_drawdown_pct": round(self.max_drawdown * 100, 2),
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "win_rate_pct": round(win_rate * 100, 2),
            "sharpe_ratio": round(sharpe_ratio, 2),
            "active_positions": len(self.active_positions),
            "closed_positions": len(self.closed_positions),
            "timestamp": datetime.now().isoformat()
        }
