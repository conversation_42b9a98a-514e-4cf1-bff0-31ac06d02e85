"""
Main Volatility Mean Reversion Trading System
The Architect's complete implementation - this is what actually makes money
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

from data_collectors.vix_collector import VIXDataCollector
from data_collectors.historical_vix_collector import RealVIXDataCollector
from strategies.volatility_mean_reversion import VolatilityMeanReversionStrategy
from risk_management.volatility_risk_manager import VolatilityRiskManager
from risk_management.tail_risk_hedging import TailRiskHedgingSystem
from risk_management.catastrophic_failure_monitor import CatastrophicFailureMonitor
from analysis.volatility_surface_analyzer import VolatilitySurfaceAnalyzer
from analysis.volatility_regime_detector import VolatilityRegimeDetector
from paper_trading.volatility_paper_trader import VolatilityPaperTrader
from utils.cache import cache_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VolatilityTradingSystem:
    """
    The Architect's Complete Volatility Trading System
    
    This is the real deal - not some academic exercise
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size

        # Initialize core components
        self.vix_collector = VIXDataCollector()
        self.real_vix_collector = RealVIXDataCollector()
        self.strategy = VolatilityMeanReversionStrategy(portfolio_size)
        self.risk_manager = VolatilityRiskManager(portfolio_size)
        self.paper_trader = VolatilityPaperTrader(portfolio_size)
        self.cache = cache_manager

        # Initialize The Architect's advanced components
        self.tail_risk_hedging = TailRiskHedgingSystem()
        self.catastrophic_monitor = CatastrophicFailureMonitor(portfolio_size)
        self.surface_analyzer = VolatilitySurfaceAnalyzer()
        self.regime_detector = VolatilityRegimeDetector()

        # Historical data for regime analysis
        self.historical_vix_data = None
        
        # System state
        self.is_running = False
        self.last_signal_time = None
        self.daily_signals_generated = 0
        self.max_daily_signals = 5  # Limit signal generation
        
        # Performance tracking
        self.system_start_time = None
        self.total_signals_generated = 0
        self.total_trades_executed = 0
        
    async def start_system(self):
        """
        Start the volatility trading system
        The Architect: This is where we begin making money
        """
        logger.info("=" * 60)
        logger.info("THE ARCHITECT'S VOLATILITY TRADING SYSTEM")
        logger.info("=" * 60)
        logger.info(f"Portfolio Size: ₹{self.portfolio_size:,.0f}")
        logger.info(f"Max Position Risk: {self.risk_manager.max_position_risk:.1%}")
        logger.info(f"Max Daily Risk: {self.risk_manager.max_daily_risk:.1%}")
        logger.info("=" * 60)
        
        self.is_running = True
        self.system_start_time = datetime.now()

        # Initialize historical VIX data for regime analysis
        logger.info("Loading historical VIX data for regime analysis...")
        async with self.real_vix_collector as collector:
            self.historical_vix_data = await collector.get_real_india_vix_data("2008-01-01")

        logger.info(f"Loaded {len(self.historical_vix_data)} historical VIX records")

        # Reset daily metrics
        self.risk_manager.reset_daily_metrics()
        
        try:
            # Main trading loop
            while self.is_running:
                await self._trading_cycle()
                await asyncio.sleep(60)  # Run every minute
                
        except KeyboardInterrupt:
            logger.info("System shutdown requested")
        except Exception as e:
            logger.error(f"System error: {e}")
        finally:
            await self.stop_system()
    
    async def _trading_cycle(self):
        """
        Execute one complete trading cycle with The Architect's enhancements
        """
        try:
            # Step 1: Check catastrophic failure conditions
            current_portfolio_value = self.paper_trader.get_portfolio_value()
            current_vix = await self._get_current_vix()
            active_positions = list(self.paper_trader.active_positions.values())

            catastrophic_check = self.catastrophic_monitor.check_catastrophic_conditions(
                current_portfolio_value, current_vix, active_positions
            )

            if catastrophic_check["catastrophic_failure_detected"]:
                logger.critical(f"CATASTROPHIC FAILURE DETECTED: {catastrophic_check['failure_type']}")

                if catastrophic_check["liquidation_required"]:
                    await self._execute_emergency_liquidation(catastrophic_check)
                    return

            # Step 2: Regime detection and filtering
            if self.historical_vix_data is not None:
                regime_analysis = self.regime_detector.detect_current_regime(self.historical_vix_data)
                regime_filters = self.regime_detector.get_regime_trading_filters(regime_analysis)

                logger.info(f"Current regime: {regime_analysis['current_regime']} "
                          f"(Risk: {regime_analysis['risk_level']})")

                if not regime_filters["allow_new_positions"]:
                    logger.warning("Regime filter: No new positions allowed")
                    await self._update_positions()
                    return

            # Step 3: Check if trading should be halted
            should_halt, reason = self.risk_manager.should_halt_trading()
            if should_halt:
                logger.warning(f"Trading halted: {reason}")
                return

            # Step 4: Check daily signal limit
            if self.daily_signals_generated >= self.max_daily_signals:
                logger.info("Daily signal limit reached")
                return

            # Step 5: Generate enhanced trading signals
            signals = await self._generate_enhanced_signals()

            if signals:
                logger.info(f"Generated {len(signals)} enhanced trading signals")

                for signal in signals:
                    await self._process_enhanced_signal(signal)

                self.daily_signals_generated += len(signals)
                self.total_signals_generated += len(signals)

            # Step 6: Update existing positions with tail risk monitoring
            await self._update_positions_with_hedging()

            # Step 7: Log enhanced system status
            if datetime.now().minute % 10 == 0:
                await self._log_enhanced_system_status()

        except Exception as e:
            logger.error(f"Error in enhanced trading cycle: {e}")
    
    async def _process_signal(self, signal: Dict[str, Any]):
        """
        Process a trading signal through risk management and execution
        The Architect: This is where discipline meets opportunity
        """
        try:
            logger.info(f"Processing signal: {signal['strategy']} - Confidence: {signal['confidence']:.1%}")
            
            # Validate signal through risk management
            is_valid, validation_message = self.risk_manager.validate_new_position(signal)
            
            if not is_valid:
                logger.warning(f"Signal rejected: {validation_message}")
                return
            
            # Calculate optimal position size
            optimal_lots = self.risk_manager.calculate_position_size(signal)
            
            if optimal_lots <= 0:
                logger.warning("Position size calculation resulted in 0 lots")
                return
            
            # Update signal with optimal sizing
            signal["lots"] = optimal_lots
            
            # Execute trade through paper trader
            executed_position = await self.paper_trader.execute_volatility_trade(signal)
            
            if executed_position:
                # Add position to risk manager tracking
                self.risk_manager.add_position(executed_position)
                self.total_trades_executed += 1
                
                logger.info(f"Trade executed: {executed_position['strategy']} - {optimal_lots} lots")
                logger.info(f"Entry premium: ₹{executed_position['entry_premium']:.2f}")
                
                # Update last signal time
                self.last_signal_time = datetime.now()
            else:
                logger.warning("Failed to execute trade")
                
        except Exception as e:
            logger.error(f"Error processing signal: {e}")

    async def _get_current_vix(self) -> float:
        """Get current VIX value"""
        try:
            async with self.vix_collector as vix:
                vix_data = await vix.get_current_vix()
                return vix_data.get("vix", 16.0)
        except:
            return 16.0  # Fallback

    async def _generate_enhanced_signals(self) -> List[Dict[str, Any]]:
        """Generate signals with volatility surface and regime analysis"""
        try:
            # Get basic signals
            basic_signals = await self.strategy.generate_signals()

            if not basic_signals:
                return []

            enhanced_signals = []

            for signal in basic_signals:
                # Get options data for surface analysis
                options_data = signal.get("options", [])
                if not options_data:
                    continue

                # Estimate spot price
                spot_price = self.strategy._estimate_spot_price(options_data)

                # Analyze volatility surface
                surface_analysis = self.surface_analyzer.analyze_volatility_surface(
                    options_data, spot_price
                )

                # Get VIX percentile data
                if self.historical_vix_data is not None:
                    vix_filters = self.real_vix_collector.get_vix_percentile_filters(
                        self.historical_vix_data
                    )

                    # Get surface-based trading signals
                    surface_signals = self.surface_analyzer.get_trading_signals_from_surface(
                        surface_analysis, vix_filters
                    )

                    # Only proceed if surface analysis confirms the signal
                    if surface_signals["trade_recommendation"] == signal["strategy"]:
                        # Enhance signal with surface analysis
                        signal["surface_analysis"] = surface_analysis
                        signal["surface_confidence"] = surface_signals["confidence"]
                        signal["vix_percentile_data"] = vix_filters

                        # Adjust overall confidence
                        original_confidence = signal.get("confidence", 0.5)
                        surface_confidence = surface_signals["confidence"]
                        signal["confidence"] = (original_confidence + surface_confidence) / 2

                        enhanced_signals.append(signal)
                    else:
                        logger.info(f"Surface analysis rejected signal: {surface_signals['reasoning']}")

            return enhanced_signals

        except Exception as e:
            logger.error(f"Error generating enhanced signals: {e}")
            return []

    async def _process_enhanced_signal(self, signal: Dict[str, Any]):
        """Process signal with tail risk hedging"""
        try:
            logger.info(f"Processing enhanced signal: {signal['strategy']} - "
                       f"Confidence: {signal['confidence']:.1%}")

            # Validate signal through risk management
            is_valid, validation_message = self.risk_manager.validate_new_position(signal)

            if not is_valid:
                logger.warning(f"Signal rejected: {validation_message}")
                return

            # Calculate optimal position size
            optimal_lots = self.risk_manager.calculate_position_size(signal)

            if optimal_lots <= 0:
                logger.warning("Position size calculation resulted in 0 lots")
                return

            # Update signal with optimal sizing
            signal["lots"] = optimal_lots

            # Execute trade through paper trader
            executed_position = await self.paper_trader.execute_volatility_trade(signal)

            if executed_position:
                # Design tail risk hedge for short volatility positions
                if signal.get("strategy") == "SHORT_VOLATILITY":
                    options_data = signal.get("options", [])
                    spot_price = self.strategy._estimate_spot_price(options_data)

                    hedge_design = self.tail_risk_hedging.design_tail_hedge(
                        executed_position, options_data, spot_price
                    )

                    if hedge_design["hedge_required"] and hedge_design["hedge_positions"]:
                        executed_position["tail_hedge"] = hedge_design
                        logger.info(f"Tail hedge designed: Cost ₹{hedge_design['hedge_cost']:.2f}, "
                                  f"Max loss reduced to ₹{hedge_design['max_loss_with_hedge']:.2f}")

                # Add position to risk manager tracking
                self.risk_manager.add_position(executed_position)
                self.total_trades_executed += 1

                logger.info(f"Enhanced trade executed: {executed_position['strategy']} - {optimal_lots} lots")
                logger.info(f"Entry premium: ₹{executed_position['entry_premium']:.2f}")

                # Update last signal time
                self.last_signal_time = datetime.now()
            else:
                logger.warning("Failed to execute enhanced trade")

        except Exception as e:
            logger.error(f"Error processing enhanced signal: {e}")

    async def _execute_emergency_liquidation(self, catastrophic_check: Dict[str, Any]):
        """Execute emergency liquidation"""
        logger.critical("EXECUTING EMERGENCY LIQUIDATION")

        # Close all active positions
        for position_id in list(self.paper_trader.active_positions.keys()):
            await self.paper_trader.close_position(position_id, f"Emergency: {catastrophic_check['failure_type']}")

        # Execute liquidation through catastrophic monitor
        active_positions = list(self.paper_trader.active_positions.values())
        liquidation_result = self.catastrophic_monitor.execute_emergency_liquidation(
            active_positions, catastrophic_check['failure_type']
        )

        logger.critical(f"Emergency liquidation completed: {liquidation_result}")

        # Stop the system
        self.is_running = False

    async def _update_positions_with_hedging(self):
        """Update positions with tail risk hedge monitoring"""
        try:
            if not self.paper_trader.active_positions:
                return

            # Get current option prices
            current_prices = await self._get_current_option_prices()

            # Update positions
            await self.paper_trader.update_positions(current_prices)

            # Monitor tail risk hedges
            current_vix = await self._get_current_vix()
            current_spot = 19500  # Approximate Nifty level

            for position_id, position in self.paper_trader.active_positions.items():
                if "tail_hedge" in position:
                    hedge_monitoring = self.tail_risk_hedging.monitor_hedge_effectiveness(
                        position, current_spot, current_vix
                    )

                    if hedge_monitoring["recommendations"]:
                        logger.info(f"Hedge monitoring for {position_id}: {hedge_monitoring['recommendations']}")

        except Exception as e:
            logger.error(f"Error updating positions with hedging: {e}")

    async def _log_enhanced_system_status(self):
        """Log enhanced system status with regime and risk analysis"""
        try:
            # Get basic performance summary
            performance = self.paper_trader.get_performance_summary()

            # Get risk status
            risk_status = self.risk_manager.get_risk_status()

            # Get catastrophic failure statistics
            failure_stats = self.catastrophic_monitor.get_failure_statistics()

            # Get regime analysis
            regime_info = "Unknown"
            if self.historical_vix_data is not None:
                regime_analysis = self.regime_detector.detect_current_regime(self.historical_vix_data)
                regime_info = f"{regime_analysis['current_regime']} (Risk: {regime_analysis['risk_level']})"

            logger.info("=" * 60)
            logger.info("ENHANCED SYSTEM STATUS")
            logger.info("=" * 60)
            logger.info(f"Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
            logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f} ({performance['total_return_pct']:+.2f}%)")
            logger.info(f"Max Drawdown: {performance['max_drawdown_pct']:.2f}%")
            logger.info(f"Win Rate: {performance['win_rate_pct']:.1f}%")
            logger.info(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
            logger.info(f"Active Positions: {performance['active_positions']}")
            logger.info(f"Volatility Regime: {regime_info}")
            logger.info(f"Failure Events: {failure_stats['total_failure_events']}")
            logger.info(f"Monthly Loss Utilization: {failure_stats['monthly_loss_limit_utilization']:.1%}")
            logger.info(f"Daily Loss Utilization: {failure_stats['daily_loss_limit_utilization']:.1%}")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error logging enhanced system status: {e}")
    
    async def _update_positions(self):
        """
        Update all active positions with current market prices
        The Architect: Keep track of your money
        """
        try:
            if not self.paper_trader.active_positions:
                return
            
            # Get current option prices (simplified - in production use real data)
            current_prices = await self._get_current_option_prices()
            
            # Update positions
            await self.paper_trader.update_positions(current_prices)
            
        except Exception as e:
            logger.error(f"Error updating positions: {e}")
    
    async def _get_current_option_prices(self) -> Dict:
        """
        Get current option prices for position updates
        The Architect: In production, this would be real-time data
        """
        # Simplified price simulation for demo
        # In production, this would fetch real NSE option prices
        import random
        
        current_prices = {}
        
        for position in self.paper_trader.active_positions.values():
            for leg in position["legs"]:
                key = (leg["strike"], leg["expiry"], leg["option_type"])
                
                # Simulate price movement (±5% random walk)
                base_price = leg["execution_price"]
                price_change = random.uniform(-0.05, 0.05)
                current_price = base_price * (1 + price_change)
                current_prices[key] = max(0.05, current_price)  # Minimum ₹0.05
        
        return current_prices
    
    async def _log_system_status(self):
        """
        Log comprehensive system status
        The Architect: Know where you stand
        """
        try:
            # Get performance summary
            performance = self.paper_trader.get_performance_summary()
            
            # Get risk status
            risk_status = self.risk_manager.get_risk_status()
            
            logger.info("=" * 50)
            logger.info("SYSTEM STATUS")
            logger.info("=" * 50)
            logger.info(f"Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
            logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f} ({performance['total_return_pct']:+.2f}%)")
            logger.info(f"Max Drawdown: {performance['max_drawdown_pct']:.2f}%")
            logger.info(f"Win Rate: {performance['win_rate_pct']:.1f}%")
            logger.info(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
            logger.info(f"Active Positions: {performance['active_positions']}")
            logger.info(f"Total Trades: {performance['total_trades']}")
            logger.info(f"Daily P&L: ₹{risk_status['daily_pnl']:,.2f}")
            logger.info(f"Available Cash: ₹{performance['available_cash']:,.2f}")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"Error logging system status: {e}")
    
    async def stop_system(self):
        """
        Gracefully stop the trading system
        The Architect: Always have an exit plan
        """
        logger.info("Stopping volatility trading system...")
        
        self.is_running = False
        
        # Close all active positions
        for position_id in list(self.paper_trader.active_positions.keys()):
            await self.paper_trader.close_position(position_id, "System shutdown")
        
        # Final performance report
        await self._generate_final_report()
        
        logger.info("System stopped successfully")
    
    async def _generate_final_report(self):
        """
        Generate final performance report
        The Architect: This is your report card
        """
        try:
            performance = self.paper_trader.get_performance_summary()
            
            runtime = datetime.now() - self.system_start_time if self.system_start_time else timedelta(0)
            
            logger.info("=" * 60)
            logger.info("FINAL PERFORMANCE REPORT")
            logger.info("=" * 60)
            logger.info(f"Runtime: {runtime}")
            logger.info(f"Initial Capital: ₹{performance['initial_capital']:,.2f}")
            logger.info(f"Final Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
            logger.info(f"Total Return: {performance['total_return_pct']:+.2f}%")
            logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f}")
            logger.info(f"Max Drawdown: {performance['max_drawdown_pct']:.2f}%")
            logger.info(f"Total Trades: {performance['total_trades']}")
            logger.info(f"Winning Trades: {performance['winning_trades']}")
            logger.info(f"Win Rate: {performance['win_rate_pct']:.1f}%")
            logger.info(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
            logger.info(f"Signals Generated: {self.total_signals_generated}")
            logger.info(f"Trades Executed: {self.total_trades_executed}")
            logger.info("=" * 60)
            
            # Save report to file
            report_data = {
                "performance": performance,
                "system_stats": {
                    "runtime_seconds": runtime.total_seconds(),
                    "signals_generated": self.total_signals_generated,
                    "trades_executed": self.total_trades_executed
                },
                "timestamp": datetime.now().isoformat()
            }
            
            with open(f"volatility_trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
                json.dump(report_data, f, indent=2)
            
            logger.info("Performance report saved to file")
            
        except Exception as e:
            logger.error(f"Error generating final report: {e}")

async def main():
    """
    Main entry point for the volatility trading system
    The Architect: Let's make some money
    """
    # Initialize system with 10 lakh portfolio
    system = VolatilityTradingSystem(portfolio_size=1000000)
    
    try:
        await system.start_system()
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await system.stop_system()

if __name__ == "__main__":
    asyncio.run(main())
