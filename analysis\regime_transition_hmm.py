"""
Hidden Markov Model for Volatility Regime Transitions
The Architect's implementation - quantify regime uncertainty before trading
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
# from sklearn.mixture import GaussianMixture  # Commented out for testing
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class RegimeState:
    """Volatility regime state definition"""
    regime_id: int
    regime_name: str
    mean_vix: float
    std_vix: float
    mean_returns: float
    std_returns: float
    persistence: float  # Probability of staying in same regime

@dataclass
class RegimeTransition:
    """Regime transition analysis"""
    current_regime: int
    current_regime_name: str
    regime_probability: float
    transition_probabilities: Dict[int, float]
    uncertainty: float
    confidence: float
    expected_duration_days: float
    risk_level: str

class VolatilityRegimeHMM:
    """
    The Architect's Hidden Markov Model for Volatility Regimes
    
    Core Logic:
    - 3 volatility regimes: Low, Normal, High
    - Estimate transition probabilities from historical data
    - Don't trade if regime uncertainty > 30%
    - Regime-specific position sizing adjustments
    """
    
    def __init__(self, n_regimes: int = 3, lookback_days: int = 252):
        self.n_regimes = n_regimes
        self.lookback_days = lookback_days
        
        # Regime definitions (will be fitted from data)
        self.regimes: List[RegimeState] = []
        self.transition_matrix = None
        self.emission_params = None
        
        # Trading thresholds
        self.max_uncertainty = 0.30  # 30% max uncertainty
        self.min_confidence = 0.70   # 70% min confidence
        
        # Model state
        self.is_fitted = False
        self.last_fit_date = None
        self.current_regime_probs = None
        
    def fit_regime_model(self, vix_data: pd.DataFrame, returns_data: Optional[pd.DataFrame] = None) -> bool:
        """
        Fit HMM to historical VIX and returns data
        The Architect: Learn from history to predict the future
        """
        try:
            if len(vix_data) < self.lookback_days:
                logger.warning(f"Insufficient data for HMM fitting: {len(vix_data)} < {self.lookback_days}")
                return False
            
            # Prepare features for regime identification
            features = self._prepare_features(vix_data, returns_data)
            
            # Simple regime identification using VIX quantiles (for testing)
            # In production, would use proper Gaussian Mixture Model
            vix_values = vix_data['vix'].values
            q33 = np.percentile(vix_values, 33)
            q67 = np.percentile(vix_values, 67)

            regime_labels = np.zeros(len(vix_values), dtype=int)
            regime_labels[vix_values <= q33] = 0  # Low vol
            regime_labels[(vix_values > q33) & (vix_values <= q67)] = 1  # Normal vol
            regime_labels[vix_values > q67] = 2  # High vol
            
            # Extract regime characteristics
            self.regimes = self._extract_regime_characteristics(vix_data, returns_data, regime_labels)
            
            # Estimate transition matrix
            self.transition_matrix = self._estimate_transition_matrix(regime_labels)
            
            # Store emission parameters (simplified for testing)
            self.emission_params = {
                'means': np.array([[q33], [(q33+q67)/2], [q67]]),
                'covariances': np.array([[[1]], [[4]], [[9]]]),  # Simple variances
                'weights': np.array([0.33, 0.34, 0.33])
            }
            
            self.is_fitted = True
            self.last_fit_date = datetime.now()
            
            logger.info(f"HMM fitted successfully with {self.n_regimes} regimes")
            self._log_regime_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to fit HMM: {e}")
            return False
    
    def predict_current_regime(self, recent_vix: pd.DataFrame, 
                             recent_returns: Optional[pd.DataFrame] = None) -> RegimeTransition:
        """
        Predict current regime and transition probabilities
        The Architect: Know where you are before you decide where to go
        """
        if not self.is_fitted:
            return self._default_regime_transition()
        
        try:
            # Prepare recent features
            features = self._prepare_features(recent_vix.tail(30), recent_returns)  # Last 30 days
            
            # Calculate regime probabilities using emission probabilities
            regime_probs = self._calculate_regime_probabilities(features[-1])  # Most recent observation
            
            # Current most likely regime
            current_regime = np.argmax(regime_probs)
            current_prob = regime_probs[current_regime]
            
            # Calculate uncertainty (entropy of probability distribution)
            uncertainty = -np.sum(regime_probs * np.log(regime_probs + 1e-10))
            uncertainty_normalized = uncertainty / np.log(self.n_regimes)  # Normalize to 0-1
            
            # Confidence is inverse of uncertainty
            confidence = 1 - uncertainty_normalized
            
            # Get transition probabilities from current regime
            if self.transition_matrix is not None:
                transition_probs = {
                    i: self.transition_matrix[current_regime, i] 
                    for i in range(self.n_regimes)
                }
            else:
                transition_probs = {i: 1/self.n_regimes for i in range(self.n_regimes)}
            
            # Expected duration in current regime
            if self.transition_matrix is not None:
                persistence = self.transition_matrix[current_regime, current_regime]
                expected_duration = 1 / (1 - persistence) if persistence < 1 else float('inf')
            else:
                expected_duration = 5.0  # Default
            
            # Determine risk level
            risk_level = self._determine_risk_level(current_regime, confidence, uncertainty_normalized)
            
            return RegimeTransition(
                current_regime=current_regime,
                current_regime_name=self.regimes[current_regime].regime_name if self.regimes else f"Regime_{current_regime}",
                regime_probability=current_prob,
                transition_probabilities=transition_probs,
                uncertainty=uncertainty_normalized,
                confidence=confidence,
                expected_duration_days=expected_duration,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Failed to predict regime: {e}")
            return self._default_regime_transition()
    
    def get_trading_filters(self, regime_transition: RegimeTransition) -> Dict[str, Any]:
        """
        Get trading filters based on regime analysis
        The Architect: Only trade when the regime is clear
        """
        filters = {
            "allow_trading": False,
            "allow_new_positions": False,
            "position_size_multiplier": 0.0,
            "max_positions": 0,
            "reasoning": ""
        }
        
        # Check uncertainty threshold
        if regime_transition.uncertainty > self.max_uncertainty:
            filters["reasoning"] = f"High regime uncertainty: {regime_transition.uncertainty:.1%} > {self.max_uncertainty:.1%}"
            return filters
        
        # Check confidence threshold
        if regime_transition.confidence < self.min_confidence:
            filters["reasoning"] = f"Low regime confidence: {regime_transition.confidence:.1%} < {self.min_confidence:.1%}"
            return filters
        
        # Regime-specific trading rules
        current_regime = regime_transition.current_regime
        
        if current_regime == 0:  # Low volatility regime
            filters.update({
                "allow_trading": True,
                "allow_new_positions": True,
                "position_size_multiplier": 1.5,  # Larger positions in stable regime
                "max_positions": 5,
                "reasoning": "Low volatility regime - favorable for volatility strategies"
            })
            
        elif current_regime == 1:  # Normal volatility regime
            filters.update({
                "allow_trading": True,
                "allow_new_positions": True,
                "position_size_multiplier": 1.0,  # Normal position sizing
                "max_positions": 3,
                "reasoning": "Normal volatility regime - standard trading allowed"
            })
            
        elif current_regime == 2:  # High volatility regime
            # Only allow trading if very confident and short duration expected
            if regime_transition.confidence > 0.85 and regime_transition.expected_duration_days < 10:
                filters.update({
                    "allow_trading": True,
                    "allow_new_positions": False,  # No new positions
                    "position_size_multiplier": 0.5,  # Smaller positions
                    "max_positions": 1,
                    "reasoning": "High volatility regime - limited trading, manage existing positions"
                })
            else:
                filters["reasoning"] = "High volatility regime - too risky for new positions"
        
        return filters
    
    def _prepare_features(self, vix_data: pd.DataFrame, returns_data: Optional[pd.DataFrame]) -> np.ndarray:
        """Prepare features for regime identification"""
        features = []
        
        # VIX level
        features.append(vix_data['vix'].values)
        
        # VIX changes
        vix_changes = vix_data['vix'].pct_change().fillna(0).values
        features.append(vix_changes)
        
        # VIX volatility (rolling 5-day std)
        vix_vol = vix_data['vix'].rolling(5).std().fillna(vix_data['vix'].std()).values
        features.append(vix_vol)
        
        # If returns data available, add return features
        if returns_data is not None and len(returns_data) == len(vix_data):
            returns = returns_data.iloc[:, 0].values  # Assume first column is returns
            features.append(returns)
            
            # Return volatility
            return_vol = pd.Series(returns).rolling(5).std().fillna(pd.Series(returns).std()).values
            features.append(return_vol)
        
        return np.column_stack(features)
    
    def _extract_regime_characteristics(self, vix_data: pd.DataFrame, 
                                      returns_data: Optional[pd.DataFrame],
                                      regime_labels: np.ndarray) -> List[RegimeState]:
        """Extract characteristics for each regime"""
        regimes = []
        regime_names = ["Low_Vol", "Normal_Vol", "High_Vol"]
        
        for regime_id in range(self.n_regimes):
            mask = regime_labels == regime_id
            
            if np.sum(mask) == 0:
                continue
            
            regime_vix = vix_data['vix'].values[mask]
            mean_vix = np.mean(regime_vix)
            std_vix = np.std(regime_vix)
            
            if returns_data is not None and len(returns_data) == len(vix_data):
                regime_returns = returns_data.iloc[:, 0].values[mask]
                mean_returns = np.mean(regime_returns)
                std_returns = np.std(regime_returns)
            else:
                mean_returns = 0.0
                std_returns = 0.02  # Default 2% daily vol
            
            # Calculate persistence (probability of staying in same regime)
            transitions = np.diff(regime_labels)
            same_regime_transitions = np.sum((regime_labels[:-1] == regime_id) & (regime_labels[1:] == regime_id))
            total_regime_days = np.sum(regime_labels == regime_id)
            persistence = same_regime_transitions / max(total_regime_days - 1, 1)
            
            regimes.append(RegimeState(
                regime_id=regime_id,
                regime_name=regime_names[regime_id] if regime_id < len(regime_names) else f"Regime_{regime_id}",
                mean_vix=mean_vix,
                std_vix=std_vix,
                mean_returns=mean_returns,
                std_returns=std_returns,
                persistence=persistence
            ))
        
        # Sort regimes by mean VIX (low to high)
        regimes.sort(key=lambda x: x.mean_vix)
        
        return regimes
    
    def _estimate_transition_matrix(self, regime_labels: np.ndarray) -> np.ndarray:
        """Estimate transition probability matrix"""
        transition_matrix = np.zeros((self.n_regimes, self.n_regimes))
        
        for i in range(len(regime_labels) - 1):
            current_regime = regime_labels[i]
            next_regime = regime_labels[i + 1]
            transition_matrix[current_regime, next_regime] += 1
        
        # Normalize rows to get probabilities
        row_sums = transition_matrix.sum(axis=1)
        for i in range(self.n_regimes):
            if row_sums[i] > 0:
                transition_matrix[i, :] /= row_sums[i]
            else:
                transition_matrix[i, :] = 1 / self.n_regimes  # Uniform if no data
        
        return transition_matrix
    
    def _calculate_regime_probabilities(self, observation: np.ndarray) -> np.ndarray:
        """Calculate regime probabilities for current observation"""
        if self.emission_params is None:
            return np.ones(self.n_regimes) / self.n_regimes
        
        probs = np.zeros(self.n_regimes)
        
        for i in range(self.n_regimes):
            # Multivariate normal probability
            mean = self.emission_params['means'][i]
            cov = self.emission_params['covariances'][i]
            weight = self.emission_params['weights'][i]
            
            try:
                # Calculate log probability to avoid numerical issues
                diff = observation - mean
                log_prob = -0.5 * (diff.T @ np.linalg.inv(cov) @ diff + np.log(np.linalg.det(2 * np.pi * cov)))
                probs[i] = weight * np.exp(log_prob)
            except:
                probs[i] = weight / self.n_regimes  # Fallback
        
        # Normalize
        probs = probs / (np.sum(probs) + 1e-10)
        
        return probs
    
    def _determine_risk_level(self, regime: int, confidence: float, uncertainty: float) -> str:
        """Determine risk level based on regime and confidence"""
        if regime == 2 or uncertainty > 0.4:  # High vol regime or high uncertainty
            return "HIGH"
        elif regime == 1 and confidence > 0.8:  # Normal vol with high confidence
            return "MEDIUM"
        elif regime == 0 and confidence > 0.8:  # Low vol with high confidence
            return "LOW"
        else:
            return "MEDIUM"
    
    def _default_regime_transition(self) -> RegimeTransition:
        """Default regime transition when model not fitted"""
        return RegimeTransition(
            current_regime=1,
            current_regime_name="Unknown",
            regime_probability=0.33,
            transition_probabilities={0: 0.33, 1: 0.34, 2: 0.33},
            uncertainty=0.5,
            confidence=0.5,
            expected_duration_days=5.0,
            risk_level="HIGH"
        )
    
    def _log_regime_summary(self):
        """Log summary of fitted regimes"""
        logger.info("Regime Summary:")
        for regime in self.regimes:
            logger.info(f"  {regime.regime_name}: VIX={regime.mean_vix:.1f}±{regime.std_vix:.1f}, "
                       f"Persistence={regime.persistence:.2f}")
    
    def get_regime_statistics(self) -> Dict[str, Any]:
        """Get comprehensive regime statistics"""
        if not self.is_fitted:
            return {"error": "Model not fitted"}
        
        return {
            "model_status": {
                "fitted": self.is_fitted,
                "last_fit": self.last_fit_date.isoformat() if self.last_fit_date else None,
                "n_regimes": self.n_regimes
            },
            "regimes": [
                {
                    "id": r.regime_id,
                    "name": r.regime_name,
                    "mean_vix": r.mean_vix,
                    "std_vix": r.std_vix,
                    "persistence": r.persistence
                } for r in self.regimes
            ],
            "transition_matrix": self.transition_matrix.tolist() if self.transition_matrix is not None else None,
            "trading_thresholds": {
                "max_uncertainty": self.max_uncertainty,
                "min_confidence": self.min_confidence
            }
        }
