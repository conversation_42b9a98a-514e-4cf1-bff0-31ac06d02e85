"""
Dynamic VIX Percentiles System
The Architect's implementation - no more static thresholds, adaptive to market regimes
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class VIXPercentileData:
    """VIX percentile analysis data"""
    current_vix: float
    percentile_rank: float
    p20_threshold: float
    p80_threshold: float
    rolling_mean: float
    rolling_std: float
    regime: str
    confidence: float
    last_calibration: datetime
    
class DynamicVIXPercentiles:
    """
    The Architect's Dynamic VIX Percentiles System
    
    Core Logic:
    - Rolling 252-day (1 year) percentile calculation
    - Long Vol: VIX < 20th percentile (extreme low volatility)
    - Short Vol: VIX > 80th percentile (extreme high volatility)
    - Monthly recalibration to adapt to regime changes
    - Statistical confidence scoring
    """
    
    def __init__(self, lookback_days: int = 252):
        self.lookback_days = lookback_days
        self.percentile_cache_file = "vix_percentiles_cache.json"
        self.last_calibration = None
        self.current_percentiles = None
        
        # Percentile thresholds
        self.long_vol_threshold = 20  # 20th percentile
        self.short_vol_threshold = 80  # 80th percentile
        
        # Confidence thresholds
        self.min_confidence = 0.7
        self.regime_stability_days = 5
        
        # Load cached percentiles if available
        self._load_percentile_cache()
    
    def calculate_dynamic_percentiles(self, vix_history: pd.DataFrame) -> VIXPercentileData:
        """
        Calculate dynamic VIX percentiles from historical data
        The Architect: This is how you adapt to changing markets
        """
        if len(vix_history) < self.lookback_days:
            logger.warning(f"Insufficient VIX history: {len(vix_history)} < {self.lookback_days}")
            return self._get_fallback_percentiles(vix_history)
        
        # Get rolling window
        rolling_window = vix_history.tail(self.lookback_days).copy()
        current_vix = vix_history.iloc[-1]['vix']
        
        # Calculate percentiles
        vix_values = rolling_window['vix'].values
        p20_threshold = np.percentile(vix_values, self.long_vol_threshold)
        p80_threshold = np.percentile(vix_values, self.short_vol_threshold)
        
        # Calculate current percentile rank
        percentile_rank = (vix_values < current_vix).sum() / len(vix_values) * 100
        
        # Calculate rolling statistics
        rolling_mean = np.mean(vix_values)
        rolling_std = np.std(vix_values)
        
        # Determine regime and confidence
        regime, confidence = self._determine_regime_and_confidence(
            current_vix, percentile_rank, p20_threshold, p80_threshold, rolling_std
        )
        
        percentile_data = VIXPercentileData(
            current_vix=current_vix,
            percentile_rank=percentile_rank,
            p20_threshold=p20_threshold,
            p80_threshold=p80_threshold,
            rolling_mean=rolling_mean,
            rolling_std=rolling_std,
            regime=regime,
            confidence=confidence,
            last_calibration=datetime.now()
        )
        
        # Cache the results
        self._save_percentile_cache(percentile_data)
        self.current_percentiles = percentile_data
        
        logger.info(f"VIX Percentiles Updated: Current={current_vix:.2f}, "
                   f"Rank={percentile_rank:.1f}%, P20={p20_threshold:.2f}, "
                   f"P80={p80_threshold:.2f}, Regime={regime}")
        
        return percentile_data
    
    def _determine_regime_and_confidence(self, current_vix: float, percentile_rank: float,
                                       p20: float, p80: float, rolling_std: float) -> Tuple[str, float]:
        """
        Determine volatility regime and confidence level
        The Architect: Know what regime you're in before you trade
        """
        # Base confidence on distance from thresholds
        if percentile_rank <= self.long_vol_threshold:
            regime = "EXTREME_LOW_VOL"
            # Confidence increases as we get further below 20th percentile
            distance_below = (self.long_vol_threshold - percentile_rank) / self.long_vol_threshold
            confidence = min(0.95, 0.6 + distance_below * 0.35)
            
        elif percentile_rank >= self.short_vol_threshold:
            regime = "EXTREME_HIGH_VOL"
            # Confidence increases as we get further above 80th percentile
            distance_above = (percentile_rank - self.short_vol_threshold) / (100 - self.short_vol_threshold)
            confidence = min(0.95, 0.6 + distance_above * 0.35)
            
        elif percentile_rank <= 40:
            regime = "LOW_VOL"
            confidence = 0.5 + (40 - percentile_rank) / 40 * 0.2
            
        elif percentile_rank >= 60:
            regime = "HIGH_VOL"
            confidence = 0.5 + (percentile_rank - 60) / 40 * 0.2
            
        else:
            regime = "NORMAL_VOL"
            # Lower confidence in normal regime
            confidence = 0.3 + (1 - abs(percentile_rank - 50) / 50) * 0.2
        
        # Adjust confidence based on volatility stability
        # Higher rolling std = lower confidence (more uncertain regime)
        vol_of_vol = rolling_std / current_vix if current_vix > 0 else 1
        stability_adjustment = max(0.5, 1 - vol_of_vol)
        confidence *= stability_adjustment
        
        return regime, confidence

    def get_trading_signals(self, vix_data: VIXPercentileData) -> Dict[str, any]:
        """
        Generate trading signals based on dynamic percentiles
        The Architect: Only trade when the odds are heavily in your favor
        """
        signals = {
            "long_volatility": False,
            "short_volatility": False,
            "regime": vix_data.regime,
            "confidence": vix_data.confidence,
            "reasoning": "",
            "position_size_multiplier": 1.0
        }

        # Only trade in extreme regimes with high confidence
        if vix_data.confidence < self.min_confidence:
            signals["reasoning"] = f"Low confidence: {vix_data.confidence:.2f} < {self.min_confidence}"
            return signals

        if vix_data.regime == "EXTREME_LOW_VOL":
            signals["long_volatility"] = True
            signals["reasoning"] = f"VIX at {vix_data.percentile_rank:.1f}% percentile (< 20th), high confidence mean reversion"
            # Higher confidence = larger position
            signals["position_size_multiplier"] = min(2.0, vix_data.confidence * 1.5)

        elif vix_data.regime == "EXTREME_HIGH_VOL":
            signals["short_volatility"] = True
            signals["reasoning"] = f"VIX at {vix_data.percentile_rank:.1f}% percentile (> 80th), high confidence mean reversion"
            # Higher confidence = larger position
            signals["position_size_multiplier"] = min(2.0, vix_data.confidence * 1.5)

        else:
            signals["reasoning"] = f"No extreme regime detected: {vix_data.regime} at {vix_data.percentile_rank:.1f}%"

        return signals

    def should_recalibrate(self) -> bool:
        """Check if monthly recalibration is needed"""
        if self.last_calibration is None:
            return True

        days_since_calibration = (datetime.now() - self.last_calibration).days
        return days_since_calibration >= 30  # Monthly recalibration

    def _get_fallback_percentiles(self, vix_history: pd.DataFrame) -> VIXPercentileData:
        """Fallback percentiles when insufficient data"""
        if len(vix_history) == 0:
            current_vix = 16.0  # Default VIX
        else:
            current_vix = vix_history.iloc[-1]['vix']

        # Use historical averages as fallback
        return VIXPercentileData(
            current_vix=current_vix,
            percentile_rank=50.0,
            p20_threshold=12.0,  # Historical 20th percentile
            p80_threshold=25.0,  # Historical 80th percentile
            rolling_mean=16.0,
            rolling_std=4.0,
            regime="NORMAL_VOL",
            confidence=0.3,  # Low confidence with insufficient data
            last_calibration=datetime.now()
        )

    def _save_percentile_cache(self, data: VIXPercentileData):
        """Save percentile data to cache"""
        try:
            cache_data = {
                "current_vix": data.current_vix,
                "percentile_rank": data.percentile_rank,
                "p20_threshold": data.p20_threshold,
                "p80_threshold": data.p80_threshold,
                "rolling_mean": data.rolling_mean,
                "rolling_std": data.rolling_std,
                "regime": data.regime,
                "confidence": data.confidence,
                "last_calibration": data.last_calibration.isoformat()
            }

            with open(self.percentile_cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            self.last_calibration = data.last_calibration

        except Exception as e:
            logger.warning(f"Failed to save percentile cache: {e}")

    def _load_percentile_cache(self):
        """Load percentile data from cache"""
        try:
            if os.path.exists(self.percentile_cache_file):
                with open(self.percentile_cache_file, 'r') as f:
                    cache_data = json.load(f)

                self.current_percentiles = VIXPercentileData(
                    current_vix=cache_data["current_vix"],
                    percentile_rank=cache_data["percentile_rank"],
                    p20_threshold=cache_data["p20_threshold"],
                    p80_threshold=cache_data["p80_threshold"],
                    rolling_mean=cache_data["rolling_mean"],
                    rolling_std=cache_data["rolling_std"],
                    regime=cache_data["regime"],
                    confidence=cache_data["confidence"],
                    last_calibration=datetime.fromisoformat(cache_data["last_calibration"])
                )

                self.last_calibration = self.current_percentiles.last_calibration
                logger.info("Loaded VIX percentiles from cache")

        except Exception as e:
            logger.warning(f"Failed to load percentile cache: {e}")

    def get_current_percentiles(self) -> Optional[VIXPercentileData]:
        """Get current percentile data"""
        return self.current_percentiles

    def get_regime_statistics(self) -> Dict[str, any]:
        """Get detailed regime statistics"""
        if not self.current_percentiles:
            return {"error": "No percentile data available"}

        data = self.current_percentiles

        return {
            "current_regime": data.regime,
            "confidence": data.confidence,
            "vix_current": data.current_vix,
            "vix_percentile": data.percentile_rank,
            "thresholds": {
                "p20_long_vol": data.p20_threshold,
                "p80_short_vol": data.p80_threshold
            },
            "statistics": {
                "rolling_mean": data.rolling_mean,
                "rolling_std": data.rolling_std,
                "vol_of_vol": data.rolling_std / data.current_vix if data.current_vix > 0 else 0
            },
            "calibration": {
                "last_calibration": data.last_calibration.isoformat(),
                "days_since_calibration": (datetime.now() - data.last_calibration).days,
                "needs_recalibration": self.should_recalibrate()
            }
        }
