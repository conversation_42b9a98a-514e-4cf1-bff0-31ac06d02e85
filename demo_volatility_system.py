"""
Demo Script for The Architect's Volatility Trading System
Shows the system in action with real market data
"""
import asyncio
import logging
from datetime import datetime

from main_volatility_system import VolatilityTradingSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_system():
    """
    Run a short demo of the volatility trading system
    The Architect: This shows the system in action
    """
    logger.info("=" * 60)
    logger.info("THE ARCHITECT'S VOLATILITY TRADING SYSTEM - DEMO")
    logger.info("=" * 60)
    
    # Initialize system with 10 lakh portfolio
    system = VolatilityTradingSystem(portfolio_size=1000000)
    
    try:
        # Run for a few cycles to demonstrate functionality
        logger.info("Starting demo - will run for 5 trading cycles...")
        
        system.is_running = True
        system.system_start_time = datetime.now()
        
        # Reset daily metrics
        system.risk_manager.reset_daily_metrics()
        
        # Run 5 trading cycles
        for cycle in range(5):
            logger.info(f"\n--- Trading Cycle {cycle + 1} ---")
            
            try:
                await system._trading_cycle()
                
                # Show current status
                performance = system.paper_trader.get_performance_summary()
                risk_status = system.risk_manager.get_risk_status()
                
                logger.info(f"Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
                logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f}")
                logger.info(f"Active Positions: {performance['active_positions']}")
                logger.info(f"Available Cash: ₹{performance['available_cash']:,.2f}")
                
                # Wait between cycles
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error in cycle {cycle + 1}: {e}")
                continue
        
        # Generate final report
        await system._generate_final_report()
        
    except Exception as e:
        logger.error(f"Demo error: {e}")
    finally:
        # Clean shutdown
        system.is_running = False
        
        # Close any open positions
        for position_id in list(system.paper_trader.active_positions.keys()):
            await system.paper_trader.close_position(position_id, "Demo end")
        
        logger.info("Demo completed successfully!")

if __name__ == "__main__":
    asyncio.run(demo_system())
