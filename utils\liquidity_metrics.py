"""
Real Liquidity Metrics Framework
The Architect's implementation - no more trading illiquid garbage
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from models.data_models import OptionsData

logger = logging.getLogger(__name__)

@dataclass
class LiquidityMetrics:
    """Comprehensive liquidity metrics for options"""
    symbol: str
    strike: float
    expiry: datetime
    option_type: str
    
    # Basic metrics
    bid_price: float
    ask_price: float
    mid_price: float
    last_price: float
    volume: int
    open_interest: int
    
    # Advanced liquidity metrics
    effective_spread: float
    effective_spread_pct: float
    market_depth_bid: float
    market_depth_ask: float
    total_market_depth: float
    depth_weighted_spread: float
    
    # Liquidity scores
    liquidity_score: float
    tradeable: bool
    rejection_reason: Optional[str]
    
    # Market impact estimates
    estimated_impact_1lot: float
    estimated_impact_5lot: float
    estimated_impact_10lot: float

class RealLiquidityMetrics:
    """
    The Architect's Real Liquidity Metrics Framework
    
    Core Principles:
    - Effective spreads matter more than quoted spreads
    - Market depth determines real execution costs
    - Reject anything with >5% effective spread
    - Require minimum ₹50,000 market depth
    - Calculate realistic market impact
    """
    
    def __init__(self):
        # Liquidity thresholds
        self.max_effective_spread_pct = 5.0  # 5% maximum
        self.min_market_depth = 50000  # ₹50,000 minimum
        self.min_volume = 100
        self.min_open_interest = 500
        
        # Market impact parameters
        self.impact_coefficient = 0.1  # 10 basis points per ₹10,000 order
        self.depth_decay_factor = 0.5  # How quickly depth decays with size
        
        # NSE lot size
        self.nse_lot_size = 75
    
    def calculate_liquidity_metrics(self, option: OptionsData, 
                                  order_book_depth: Optional[Dict] = None) -> LiquidityMetrics:
        """
        Calculate comprehensive liquidity metrics for an option
        The Architect: This is how you separate liquid from illiquid
        """
        # Basic price metrics
        bid_price = max(0.05, option.bid_price)  # Minimum tick size
        ask_price = max(bid_price + 0.05, option.ask_price)
        mid_price = (bid_price + ask_price) / 2
        last_price = option.last_price if option.last_price > 0 else mid_price
        
        # Calculate effective spread
        effective_spread = ask_price - bid_price
        effective_spread_pct = (effective_spread / mid_price) * 100 if mid_price > 0 else 100
        
        # Estimate market depth (if order book not available)
        if order_book_depth is None:
            market_depth_bid, market_depth_ask = self._estimate_market_depth(option)
        else:
            market_depth_bid = order_book_depth.get('bid_depth', 0)
            market_depth_ask = order_book_depth.get('ask_depth', 0)
        
        total_market_depth = market_depth_bid + market_depth_ask
        
        # Calculate depth-weighted spread
        if total_market_depth > 0:
            bid_weight = market_depth_bid / total_market_depth
            ask_weight = market_depth_ask / total_market_depth
            depth_weighted_spread = effective_spread * (1 - min(bid_weight, ask_weight))
        else:
            depth_weighted_spread = effective_spread * 2  # Penalty for no depth
        
        # Calculate market impact estimates
        impact_1lot = self._calculate_market_impact(1, mid_price, total_market_depth)
        impact_5lot = self._calculate_market_impact(5, mid_price, total_market_depth)
        impact_10lot = self._calculate_market_impact(10, mid_price, total_market_depth)
        
        # Calculate overall liquidity score (0-100)
        liquidity_score = self._calculate_liquidity_score(
            effective_spread_pct, total_market_depth, option.volume, option.open_interest
        )
        
        # Determine if tradeable
        tradeable, rejection_reason = self._assess_tradeability(
            effective_spread_pct, total_market_depth, option.volume, option.open_interest
        )
        
        return LiquidityMetrics(
            symbol=option.symbol,
            strike=option.strike,
            expiry=option.expiry_date,
            option_type=option.option_type,
            bid_price=bid_price,
            ask_price=ask_price,
            mid_price=mid_price,
            last_price=last_price,
            volume=option.volume,
            open_interest=option.open_interest,
            effective_spread=effective_spread,
            effective_spread_pct=effective_spread_pct,
            market_depth_bid=market_depth_bid,
            market_depth_ask=market_depth_ask,
            total_market_depth=total_market_depth,
            depth_weighted_spread=depth_weighted_spread,
            liquidity_score=liquidity_score,
            tradeable=tradeable,
            rejection_reason=rejection_reason,
            estimated_impact_1lot=impact_1lot,
            estimated_impact_5lot=impact_5lot,
            estimated_impact_10lot=impact_10lot
        )
    
    def _estimate_market_depth(self, option: OptionsData) -> Tuple[float, float]:
        """
        Estimate market depth based on volume and open interest
        The Architect: When you don't have order book, estimate conservatively
        """
        # Base depth on volume and open interest
        volume_factor = min(option.volume, 1000) / 1000  # Cap at 1000 for calculation
        oi_factor = min(option.open_interest, 5000) / 5000  # Cap at 5000
        
        # Estimate depth as percentage of notional value
        notional_value = option.strike * self.nse_lot_size
        base_depth_pct = 0.01 + (volume_factor * 0.02) + (oi_factor * 0.03)  # 1-6%
        
        estimated_depth = notional_value * base_depth_pct
        
        # Split between bid and ask (slightly favor ask for calls, bid for puts)
        if option.option_type == "CE":
            bid_depth = estimated_depth * 0.45
            ask_depth = estimated_depth * 0.55
        else:
            bid_depth = estimated_depth * 0.55
            ask_depth = estimated_depth * 0.45
        
        return bid_depth, ask_depth
    
    def _calculate_market_impact(self, lots: int, mid_price: float, market_depth: float) -> float:
        """
        Calculate estimated market impact for given lot size
        The Architect: Know your impact before you trade
        """
        if market_depth <= 0:
            return mid_price * 0.1  # 10% impact if no depth
        
        order_value = lots * self.nse_lot_size * mid_price
        
        # Impact increases non-linearly with order size vs depth
        depth_ratio = order_value / market_depth
        
        # Base impact + size penalty
        base_impact = self.impact_coefficient * depth_ratio
        size_penalty = (depth_ratio ** 1.5) * 0.05  # Accelerating impact
        
        total_impact_pct = base_impact + size_penalty
        
        # Cap impact at 50% of mid price
        return min(mid_price * 0.5, mid_price * total_impact_pct)
    
    def _calculate_liquidity_score(self, spread_pct: float, depth: float, 
                                 volume: int, oi: int) -> float:
        """
        Calculate overall liquidity score (0-100)
        The Architect: One number to rule them all
        """
        # Spread component (0-40 points)
        if spread_pct <= 1:
            spread_score = 40
        elif spread_pct <= 3:
            spread_score = 40 - (spread_pct - 1) * 15  # Linear decay
        elif spread_pct <= 5:
            spread_score = 10 - (spread_pct - 3) * 5   # Faster decay
        else:
            spread_score = 0
        
        # Depth component (0-30 points)
        if depth >= 100000:
            depth_score = 30
        elif depth >= 50000:
            depth_score = 20 + (depth - 50000) / 50000 * 10
        elif depth >= 10000:
            depth_score = 5 + (depth - 10000) / 40000 * 15
        else:
            depth_score = depth / 10000 * 5
        
        # Volume component (0-15 points)
        volume_score = min(15, volume / 100 * 15)
        
        # Open interest component (0-15 points)
        oi_score = min(15, oi / 1000 * 15)
        
        total_score = spread_score + depth_score + volume_score + oi_score
        return min(100, max(0, total_score))
    
    def _assess_tradeability(self, spread_pct: float, depth: float, 
                           volume: int, oi: int) -> Tuple[bool, Optional[str]]:
        """
        Assess if option is tradeable based on liquidity criteria
        The Architect: Hard filters to keep you out of trouble
        """
        # Check effective spread
        if spread_pct > self.max_effective_spread_pct:
            return False, f"Effective spread {spread_pct:.1f}% > {self.max_effective_spread_pct}%"
        
        # Check market depth
        if depth < self.min_market_depth:
            return False, f"Market depth ₹{depth:,.0f} < ₹{self.min_market_depth:,}"
        
        # Check volume
        if volume < self.min_volume:
            return False, f"Volume {volume} < {self.min_volume}"
        
        # Check open interest
        if oi < self.min_open_interest:
            return False, f"Open interest {oi} < {self.min_open_interest}"
        
        return True, None
    
    def filter_tradeable_options(self, options: List[OptionsData]) -> List[LiquidityMetrics]:
        """
        Filter options list to only tradeable ones
        The Architect: Quality over quantity
        """
        tradeable_options = []
        
        for option in options:
            metrics = self.calculate_liquidity_metrics(option)
            if metrics.tradeable:
                tradeable_options.append(metrics)
        
        # Sort by liquidity score (best first)
        tradeable_options.sort(key=lambda x: x.liquidity_score, reverse=True)
        
        logger.info(f"Filtered {len(tradeable_options)} tradeable options from {len(options)} total")
        
        return tradeable_options
    
    def get_execution_cost_estimate(self, metrics: LiquidityMetrics, lots: int) -> Dict[str, float]:
        """
        Get comprehensive execution cost estimate
        The Architect: Know what you're paying before you pay it
        """
        # Base costs
        spread_cost = metrics.effective_spread * lots * self.nse_lot_size
        
        # Market impact based on lot size
        if lots <= 1:
            impact_cost = metrics.estimated_impact_1lot * lots * self.nse_lot_size
        elif lots <= 5:
            impact_cost = metrics.estimated_impact_5lot * lots * self.nse_lot_size
        else:
            impact_cost = metrics.estimated_impact_10lot * lots * self.nse_lot_size
        
        # NSE transaction costs (approximate)
        notional_value = metrics.mid_price * lots * self.nse_lot_size
        transaction_cost = notional_value * 0.0005  # 5 basis points
        
        total_cost = spread_cost + impact_cost + transaction_cost
        
        return {
            "spread_cost": spread_cost,
            "impact_cost": impact_cost,
            "transaction_cost": transaction_cost,
            "total_cost": total_cost,
            "cost_pct": (total_cost / notional_value) * 100 if notional_value > 0 else 0
        }
