"""
VIX Data Collector for Volatility Mean Reversion Trading System
The Architect's implementation - focused on real trading, not manipulation detection
"""
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import json
import time
import numpy as np

from config.settings import settings
from utils.cache import cache_manager
from utils.exceptions import DataCollectionError

logger = logging.getLogger(__name__)

class VIXDataCollector:
    """
    Specialized VIX data collector for volatility mean reversion strategy
    The Architect: This is what actually matters for making money
    """
    
    def __init__(self):
        self.base_url = "https://www.nseindia.com/api"
        self.headers = self._get_headers()
        self.cache = cache_manager
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = asyncio.Semaphore(2)  # Conservative rate limiting
        self.last_request_time = 0
        
    def _get_headers(self) -> Dict[str, str]:
        """Get proper headers for NSE VIX API"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.nseindia.com/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _rate_limited_request(self, url: str, params: Dict = None) -> Dict:
        """Make rate-limited request to NSE API"""
        async with self.rate_limiter:
            # Ensure minimum time between requests
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            min_interval = 0.5  # 500ms between requests
            
            if time_since_last < min_interval:
                await asyncio.sleep(min_interval - time_since_last)
            
            try:
                async with self.session.get(url, params=params) as response:
                    self.last_request_time = time.time()
                    
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"NSE API error: {response.status} for {url}")
                        raise DataCollectionError(f"NSE API returned {response.status}")
                        
            except aiohttp.ClientError as e:
                logger.error(f"Network error fetching {url}: {e}")
                raise DataCollectionError(f"Network error: {e}")
    
    async def get_current_vix(self) -> Dict[str, Any]:
        """
        Get current VIX data from NSE
        The Architect: This is the core data we need for volatility trading
        """
        cache_key = "vix_current"

        # Check cache first (cache for 60 seconds)
        cached_data = await self.cache.get(cache_key)
        if cached_data:
            return cached_data

        # Try to get real VIX data first
        vix_value = None
        vix_change = None
        vix_percent_change = None
        source = "SIMULATED"

        try:
            # NSE VIX endpoint
            url = f"{self.base_url}/equity-stockIndices"
            params = {"index": "INDIA VIX"}

            data = await self._rate_limited_request(url, params)

            # Extract VIX from the response
            if "data" in data and len(data["data"]) > 0:
                vix_data = data["data"][0]
                vix_value = float(vix_data.get("last", 0))
                vix_change = float(vix_data.get("change", 0))
                vix_percent_change = float(vix_data.get("pChange", 0))
                source = "NSE"

        except Exception as e:
            logger.warning(f"NSE API failed: {e}, using simulated data")

        # Fallback to simulated data if API fails
        if vix_value is None or vix_value == 0:
            # Generate realistic VIX for testing/demo
            # The Architect: In production, you'd have proper data feeds like Bloomberg
            vix_value = np.random.normal(16.0, 3.0)
            vix_value = max(8.0, min(50.0, vix_value))
            vix_change = np.random.normal(0, 0.5)
            vix_percent_change = (vix_change / vix_value) * 100
            source = "SIMULATED"

            logger.info("Using simulated VIX data for demo - get proper data feeds for production!")

        result = {
            "vix": round(vix_value, 2),
            "change": round(vix_change, 2),
            "percent_change": round(vix_percent_change, 2),
            "timestamp": datetime.now().isoformat(),
            "source": source
        }

        # Cache for 60 seconds
        await self.cache.set(cache_key, result, ttl=60)

        logger.info(f"VIX: {vix_value:.2f} ({vix_change:+.2f}, {vix_percent_change:+.2f}%) [{source}]")
        return result
    
    async def get_vix_historical(self, days: int = 30) -> pd.DataFrame:
        """
        Get historical VIX data for strategy backtesting
        The Architect: This is where we validate our edge
        """
        cache_key = f"vix_historical_{days}"
        
        # Check cache first
        cached_data = await self.cache.get(cache_key)
        if cached_data:
            return pd.DataFrame(cached_data)
        
        try:
            # Generate realistic VIX historical data for backtesting
            # The Architect: In production, use proper historical data provider
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # Simulate VIX with realistic mean reversion characteristics
            np.random.seed(42)  # Reproducible for testing
            vix_values = []
            current_vix = 15.0
            
            for i in range(len(dates)):
                # Mean reversion parameters based on historical VIX behavior
                mean_vix = 16.0
                reversion_speed = 0.15
                volatility = 0.25
                
                # Add volatility clustering (GARCH-like behavior)
                if i > 0 and abs(vix_values[-1] - mean_vix) > 5:
                    volatility *= 1.5  # Higher vol during stress
                
                # Mean reversion component
                reversion = reversion_speed * (mean_vix - current_vix)
                
                # Random shock with fat tails
                if np.random.random() < 0.05:  # 5% chance of extreme move
                    shock = np.random.normal(0, volatility * 3)
                else:
                    shock = np.random.normal(0, volatility)
                
                # Update VIX with bounds
                current_vix = max(8.0, min(50.0, current_vix + reversion + shock))
                vix_values.append(current_vix)
            
            # Create DataFrame with technical indicators
            df = pd.DataFrame({
                'date': dates,
                'vix': vix_values
            })
            
            # Add moving averages for strategy
            df['vix_ma_10'] = df['vix'].rolling(10).mean()
            df['vix_ma_20'] = df['vix'].rolling(20).mean()
            df['vix_std_20'] = df['vix'].rolling(20).std()
            
            # Add volume proxy (higher volume during spikes)
            df['volume_proxy'] = np.where(
                df['vix'] > df['vix_ma_20'], 
                np.random.normal(1.5, 0.3, len(df)),
                np.random.normal(1.0, 0.2, len(df))
            )
            
            # Cache for 1 hour
            await self.cache.set(cache_key, df.to_dict('records'), ttl=3600)
            
            logger.info(f"Generated {len(df)} historical VIX records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to generate historical VIX data: {e}")
            raise DataCollectionError(f"Historical VIX data generation failed: {e}")
    
    def calculate_vix_signals(self, vix_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate VIX-based trading signals using The Architect's strategy
        
        Entry Conditions:
        - Long Vol: VIX < 12 AND below 10-day MA for 2+ consecutive days
        - Short Vol: VIX > 25 AND above 10-day MA with volume spike
        """
        if len(vix_data) < 20:
            return {
                "signal": "NO_DATA", 
                "confidence": 0.0,
                "reason": "Insufficient data for analysis"
            }
        
        # Current values
        current_vix = vix_data['vix'].iloc[-1]
        vix_ma_10 = vix_data['vix_ma_10'].iloc[-1]
        vix_ma_20 = vix_data['vix_ma_20'].iloc[-1]
        volume_proxy = vix_data['volume_proxy'].iloc[-1]
        
        # Calculate percentiles for context
        vix_20_percentile = vix_data['vix'].quantile(0.2)
        vix_80_percentile = vix_data['vix'].quantile(0.8)
        
        signal = "HOLD"
        confidence = 0.0
        reason = "No clear signal"
        
        # Long volatility conditions (buy straddles/strangles)
        if current_vix < 12 and current_vix < vix_ma_10:
            # Check for consecutive days below MA
            consecutive_below = 0
            for i in range(min(5, len(vix_data))):
                idx = -(i+1)
                if (vix_data['vix'].iloc[idx] < vix_data['vix_ma_10'].iloc[idx] and 
                    vix_data['vix'].iloc[idx] < 12):
                    consecutive_below += 1
                else:
                    break
            
            if consecutive_below >= 2:
                signal = "LONG_VOLATILITY"
                confidence = min(0.9, 0.6 + (consecutive_below * 0.1))
                reason = f"VIX {current_vix:.1f} < 12 and below MA for {consecutive_below} days"
        
        # Short volatility conditions (sell straddles/strangles)
        elif current_vix > 25 and current_vix > vix_ma_10:
            # Check for volume spike
            avg_volume = vix_data['volume_proxy'].tail(5).mean()
            volume_spike = volume_proxy > (avg_volume * 1.5)
            
            if volume_spike:
                signal = "SHORT_VOLATILITY"
                confidence = min(0.9, 0.7 + min(0.2, (current_vix - 25) / 50))
                reason = f"VIX {current_vix:.1f} > 25 with volume spike"
        
        return {
            "signal": signal,
            "confidence": round(confidence, 3),
            "reason": reason,
            "current_vix": round(current_vix, 2),
            "vix_ma_10": round(vix_ma_10, 2),
            "vix_ma_20": round(vix_ma_20, 2),
            "vix_percentile_20": round(vix_20_percentile, 2),
            "vix_percentile_80": round(vix_80_percentile, 2),
            "volume_proxy": round(volume_proxy, 2),
            "timestamp": datetime.now().isoformat()
        }
