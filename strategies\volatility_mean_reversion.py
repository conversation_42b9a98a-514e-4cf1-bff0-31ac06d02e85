"""
Nifty 50 Options Volatility Mean Reversion Strategy
The Architect's implementation - real trading logic, not academic bullshit
"""
import asyncio
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import numpy as np

from data_collectors.vix_collector import VIXDataCollector
from data_collectors.nse_collector import NSEDataCollector
from models.options_greeks import OptionsGreeksCalculator
from models.data_models import OptionsData
from utils.cache import cache_manager

logger = logging.getLogger(__name__)

class VolatilityMeanReversionStrategy:
    """
    The Architect's Volatility Mean Reversion Strategy
    
    Entry Conditions:
    - Long Vol: VIX < 12 AND below 10-day MA for 2+ consecutive days
    - Short Vol: VIX > 25 AND above 10-day MA with volume spike
    
    Exit Conditions:
    - Profit Target: 50% of premium (short vol) or 100% gain (long vol)
    - Stop Loss: 200% of premium (short vol) or 50% loss (long vol)
    - Time Decay: Close 2 days before expiration
    
    Position Sizing: 1% portfolio risk per trade
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size
        self.vix_collector = VIXDataCollector()
        self.nse_collector = NSEDataCollector()
        self.greeks_calculator = OptionsGreeksCalculator()
        self.cache = cache_manager
        
        # Strategy parameters
        self.max_portfolio_risk = 0.05  # 5% daily loss limit
        self.max_position_risk = 0.01   # 1% risk per trade
        self.max_concurrent_positions = 3
        
        # VIX thresholds
        self.vix_low_threshold = 12.0
        self.vix_high_threshold = 25.0
        
        # Greeks limits
        self.max_portfolio_delta = 0.1  # 10% of portfolio
        self.max_portfolio_gamma = 0.02 # 2% of portfolio per 1% Nifty move
        
        # Current positions
        self.positions: List[Dict] = []
        self.daily_pnl = 0.0
        
    async def get_nifty_options_data(self) -> List[OptionsData]:
        """Get liquid Nifty 50 options data"""
        try:
            async with self.nse_collector as collector:
                raw_data = await collector._make_request(
                    "https://www.nseindia.com/api/option-chain-indices",
                    {"symbol": "NIFTY"}
                )
                
                if "records" not in raw_data:
                    logger.error("No options data received")
                    return []
                
                options_list = []
                data = raw_data["records"]["data"]
                timestamp = datetime.now()
                
                for item in data:
                    # Only process options with good liquidity
                    if "CE" in item:
                        ce_data = item["CE"]
                        oi = int(ce_data.get("openInterest", 0))
                        volume = int(ce_data.get("totalTradedVolume", 0))
                        
                        # The Architect: Only liquid options
                        if oi > 500 and volume > 100:
                            options_list.append(OptionsData(
                                symbol="NIFTY",
                                strike=float(item["strikePrice"]),
                                expiry_date=datetime.strptime(item["expiryDate"], "%d-%b-%Y"),
                                option_type="CE",
                                last_price=float(ce_data.get("lastPrice", 0)),
                                volume=volume,
                                open_interest=oi,
                                bid_price=float(ce_data.get("bidprice", 0)),
                                ask_price=float(ce_data.get("askPrice", 0)),
                                bid_qty=int(ce_data.get("bidQty", 0)),
                                ask_qty=int(ce_data.get("askQty", 0)),
                                implied_volatility=float(ce_data.get("impliedVolatility", 0)),
                                timestamp=timestamp
                            ))
                    
                    if "PE" in item:
                        pe_data = item["PE"]
                        oi = int(pe_data.get("openInterest", 0))
                        volume = int(pe_data.get("totalTradedVolume", 0))
                        
                        if oi > 500 and volume > 100:
                            options_list.append(OptionsData(
                                symbol="NIFTY",
                                strike=float(item["strikePrice"]),
                                expiry_date=datetime.strptime(item["expiryDate"], "%d-%b-%Y"),
                                option_type="PE",
                                last_price=float(pe_data.get("lastPrice", 0)),
                                volume=volume,
                                open_interest=oi,
                                bid_price=float(pe_data.get("bidprice", 0)),
                                ask_price=float(pe_data.get("askPrice", 0)),
                                bid_qty=int(pe_data.get("bidQty", 0)),
                                ask_qty=int(pe_data.get("askQty", 0)),
                                implied_volatility=float(pe_data.get("impliedVolatility", 0)),
                                timestamp=timestamp
                            ))
                
                logger.info(f"Retrieved {len(options_list)} liquid options")
                return options_list
                
        except Exception as e:
            logger.error(f"Failed to get options data: {e}")
            return []
    
    def filter_tradeable_options(self, options_data: List[OptionsData],
                                spot_price: float) -> List[OptionsData]:
        """
        Filter options for trading based on The Architect's criteria
        - 7-14 DTE only
        - Bid-ask spread < 5% of mid-price
        - Open interest > 500
        """
        tradeable = []
        current_date = datetime.now().date()

        for option in options_data:
            # Check DTE - handle both date and datetime objects
            expiry_date = option.expiry_date
            if isinstance(expiry_date, datetime):
                expiry_date = expiry_date.date()

            dte = (expiry_date - current_date).days
            if not (7 <= dte <= 14):
                continue
            
            # Check bid-ask spread
            if option.bid_price > 0 and option.ask_price > 0:
                mid_price = (option.bid_price + option.ask_price) / 2
                spread_pct = (option.ask_price - option.bid_price) / mid_price
                if spread_pct > 0.05:  # 5% max spread
                    continue
            
            # Check open interest
            if option.open_interest < 500:
                continue
            
            tradeable.append(option)
        
        return tradeable
    
    def select_straddle_strikes(self, options_data: List[OptionsData], 
                               spot_price: float) -> Tuple[Optional[float], List[OptionsData]]:
        """
        Select ATM or near-ATM strikes for straddle/strangle
        The Architect: Keep it simple - ATM straddles for max vega exposure
        """
        # Group by strike and expiry
        strikes = {}
        for option in options_data:
            key = (option.strike, option.expiry_date)
            if key not in strikes:
                strikes[key] = {"CE": None, "PE": None}
            strikes[key][option.option_type] = option
        
        # Find ATM strike
        atm_strike = None
        min_distance = float('inf')
        
        for (strike, expiry), options in strikes.items():
            if options["CE"] and options["PE"]:  # Both call and put available
                distance = abs(strike - spot_price)
                if distance < min_distance:
                    min_distance = distance
                    atm_strike = strike
        
        if atm_strike:
            # Return the ATM straddle options
            for (strike, expiry), options in strikes.items():
                if strike == atm_strike and options["CE"] and options["PE"]:
                    return atm_strike, [options["CE"], options["PE"]]
        
        return None, []
    
    def calculate_position_size(self, max_loss: float, 
                               premium_per_lot: float) -> int:
        """
        Calculate position size based on 1% portfolio risk
        The Architect: Risk management is everything
        """
        max_risk_amount = self.portfolio_size * self.max_position_risk
        
        if max_loss <= 0:
            return 0
        
        # Calculate lots based on maximum loss
        lots = int(max_risk_amount / max_loss)
        
        # Ensure minimum viable position
        if lots < 1 and premium_per_lot < max_risk_amount:
            lots = 1
        
        return max(0, lots)
    
    async def generate_signals(self) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on VIX analysis
        The Architect: This is where we make money
        """
        signals = []
        
        try:
            # Get VIX data and signals
            async with self.vix_collector as vix:
                current_vix = await vix.get_current_vix()
                historical_vix = await vix.get_vix_historical(30)
                vix_signal = vix.calculate_vix_signals(historical_vix)
            
            # Get options data
            options_data = await self.get_nifty_options_data()
            if not options_data:
                return signals
            
            # Estimate spot price from options
            spot_price = self._estimate_spot_price(options_data)
            
            # Filter tradeable options
            tradeable_options = self.filter_tradeable_options(options_data, spot_price)
            
            if not tradeable_options:
                logger.warning("No tradeable options found")
                return signals
            
            # Generate signal based on VIX
            if vix_signal["signal"] == "LONG_VOLATILITY" and vix_signal["confidence"] > 0.6:
                signal = self._create_long_vol_signal(
                    tradeable_options, spot_price, vix_signal
                )
                if signal:
                    signals.append(signal)
            
            elif vix_signal["signal"] == "SHORT_VOLATILITY" and vix_signal["confidence"] > 0.7:
                signal = self._create_short_vol_signal(
                    tradeable_options, spot_price, vix_signal
                )
                if signal:
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Failed to generate signals: {e}")
            return []
    
    def _estimate_spot_price(self, options_data: List[OptionsData]) -> float:
        """Estimate Nifty spot price from options data"""
        # Simple estimation: use ATM call-put parity
        atm_options = [opt for opt in options_data
                      if abs(opt.last_price - 100) < 50]  # Rough ATM filter

        if atm_options:
            return np.median([opt.strike for opt in atm_options])

        # Fallback: use middle of strike range
        strikes = [opt.strike for opt in options_data]
        return (max(strikes) + min(strikes)) / 2
    
    def _create_long_vol_signal(self, options_data: List[OptionsData], 
                               spot_price: float, vix_signal: Dict) -> Optional[Dict]:
        """Create long volatility signal (buy straddle)"""
        atm_strike, straddle_options = self.select_straddle_strikes(options_data, spot_price)
        
        if not straddle_options:
            return None
        
        # Calculate total premium
        total_premium = sum(opt.last_price for opt in straddle_options)
        
        # Position sizing (max loss = premium paid)
        lots = self.calculate_position_size(total_premium * 75, total_premium)  # 75 = lot size
        
        if lots == 0:
            return None
        
        return {
            "strategy": "LONG_VOLATILITY",
            "signal_type": "BUY_STRADDLE",
            "strike": atm_strike,
            "options": straddle_options,
            "lots": lots,
            "total_premium": total_premium,
            "max_loss": total_premium * lots * 75,
            "profit_target": total_premium * 2.0,  # 100% gain
            "stop_loss": total_premium * 0.5,     # 50% loss
            "confidence": vix_signal["confidence"],
            "vix_data": vix_signal,
            "timestamp": datetime.now().isoformat()
        }
    
    def _create_short_vol_signal(self, options_data: List[OptionsData], 
                                spot_price: float, vix_signal: Dict) -> Optional[Dict]:
        """Create short volatility signal (sell straddle)"""
        atm_strike, straddle_options = self.select_straddle_strikes(options_data, spot_price)
        
        if not straddle_options:
            return None
        
        # Calculate total premium received
        total_premium = sum(opt.last_price for opt in straddle_options)
        
        # Position sizing (max loss = much higher for short vol)
        max_loss_estimate = total_premium * 3  # Conservative estimate
        lots = self.calculate_position_size(max_loss_estimate * 75, total_premium)
        
        if lots == 0:
            return None
        
        return {
            "strategy": "SHORT_VOLATILITY",
            "signal_type": "SELL_STRADDLE",
            "strike": atm_strike,
            "options": straddle_options,
            "lots": lots,
            "total_premium": total_premium,
            "max_loss": max_loss_estimate * lots * 75,
            "profit_target": total_premium * 0.5,   # 50% of premium
            "stop_loss": total_premium * 2.0,       # 200% of premium
            "confidence": vix_signal["confidence"],
            "vix_data": vix_signal,
            "timestamp": datetime.now().isoformat()
        }
