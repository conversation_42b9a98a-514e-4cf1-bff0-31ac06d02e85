"""
Catastrophic Failure Monitoring System
The Architect's implementation - hard stops that save your account from total destruction
"""
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class CatastrophicFailureMonitor:
    """
    Catastrophic failure detection and automatic liquidation system
    The Architect: These are the circuit breakers that prevent total account destruction
    """
    
    def __init__(self, portfolio_size: float):
        self.portfolio_size = portfolio_size
        
        # Catastrophic failure thresholds
        self.max_monthly_loss_pct = 0.15  # 15% monthly loss limit
        self.max_daily_loss_pct = 0.08    # 8% daily loss limit
        self.vix_spike_threshold = 40     # VIX > 40 triggers short vol liquidation
        self.correlation_breakdown_threshold = 0.9  # When correlations spike to 0.9+
        
        # Tracking variables
        self.monthly_pnl = 0.0
        self.daily_pnl = 0.0
        self.month_start_portfolio_value = portfolio_size
        self.day_start_portfolio_value = portfolio_size
        self.last_reset_date = datetime.now().date()
        
        # Failure event tracking
        self.failure_events = []
        self.emergency_liquidations = []
        
    def check_catastrophic_conditions(self, current_portfolio_value: float,
                                    current_vix: float,
                                    active_positions: List[Dict],
                                    market_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Check for catastrophic failure conditions
        The Architect: This is your last line of defense against total destruction
        """
        failure_check = {
            "catastrophic_failure_detected": False,
            "failure_type": None,
            "severity": "NORMAL",
            "immediate_action_required": False,
            "liquidation_required": False,
            "failure_details": {},
            "recommendations": []
        }
        
        # Update daily/monthly tracking
        self._update_pnl_tracking(current_portfolio_value)
        
        # Check 1: Monthly loss limit
        monthly_loss_check = self._check_monthly_loss_limit(current_portfolio_value)
        if monthly_loss_check["triggered"]:
            failure_check.update({
                "catastrophic_failure_detected": True,
                "failure_type": "MONTHLY_LOSS_LIMIT",
                "severity": "CRITICAL",
                "immediate_action_required": True,
                "liquidation_required": True,
                "failure_details": monthly_loss_check
            })
            return failure_check
        
        # Check 2: Daily loss limit
        daily_loss_check = self._check_daily_loss_limit(current_portfolio_value)
        if daily_loss_check["triggered"]:
            failure_check.update({
                "catastrophic_failure_detected": True,
                "failure_type": "DAILY_LOSS_LIMIT",
                "severity": "CRITICAL",
                "immediate_action_required": True,
                "liquidation_required": True,
                "failure_details": daily_loss_check
            })
            return failure_check
        
        # Check 3: VIX spike with short volatility positions
        vix_spike_check = self._check_vix_spike_condition(current_vix, active_positions)
        if vix_spike_check["triggered"]:
            failure_check.update({
                "catastrophic_failure_detected": True,
                "failure_type": "VIX_SPIKE_SHORT_VOL",
                "severity": "CRITICAL",
                "immediate_action_required": True,
                "liquidation_required": True,
                "failure_details": vix_spike_check
            })
            return failure_check
        
        # Check 4: Correlation breakdown (if market data available)
        if market_data:
            correlation_check = self._check_correlation_breakdown(market_data)
            if correlation_check["triggered"]:
                failure_check.update({
                    "catastrophic_failure_detected": True,
                    "failure_type": "CORRELATION_BREAKDOWN",
                    "severity": "HIGH",
                    "immediate_action_required": True,
                    "liquidation_required": False,  # Reduce positions, don't liquidate all
                    "failure_details": correlation_check
                })
                return failure_check
        
        # Check 5: Portfolio Greeks explosion
        greeks_check = self._check_portfolio_greeks_explosion(active_positions)
        if greeks_check["triggered"]:
            failure_check.update({
                "catastrophic_failure_detected": True,
                "failure_type": "GREEKS_EXPLOSION",
                "severity": "HIGH",
                "immediate_action_required": True,
                "liquidation_required": False,
                "failure_details": greeks_check
            })
            return failure_check
        
        # Check 6: Liquidity crisis
        liquidity_check = self._check_liquidity_crisis(active_positions)
        if liquidity_check["triggered"]:
            failure_check.update({
                "catastrophic_failure_detected": True,
                "failure_type": "LIQUIDITY_CRISIS",
                "severity": "HIGH",
                "immediate_action_required": True,
                "liquidation_required": False,
                "failure_details": liquidity_check
            })
        
        # Warning conditions (not catastrophic yet, but concerning)
        warning_conditions = self._check_warning_conditions(
            current_portfolio_value, current_vix, active_positions
        )
        
        if warning_conditions:
            failure_check["severity"] = "WARNING"
            failure_check["recommendations"] = warning_conditions
        
        return failure_check
    
    def _update_pnl_tracking(self, current_portfolio_value: float):
        """Update daily and monthly P&L tracking"""
        current_date = datetime.now().date()
        
        # Reset daily tracking if new day
        if current_date != self.last_reset_date:
            self.day_start_portfolio_value = current_portfolio_value
            self.last_reset_date = current_date
        
        # Reset monthly tracking if new month
        if current_date.month != self.last_reset_date.month:
            self.month_start_portfolio_value = current_portfolio_value
        
        # Calculate current P&L
        self.daily_pnl = current_portfolio_value - self.day_start_portfolio_value
        self.monthly_pnl = current_portfolio_value - self.month_start_portfolio_value
    
    def _check_monthly_loss_limit(self, current_portfolio_value: float) -> Dict[str, Any]:
        """Check if monthly loss limit is breached"""
        monthly_loss_pct = abs(self.monthly_pnl) / self.month_start_portfolio_value
        
        return {
            "triggered": monthly_loss_pct > self.max_monthly_loss_pct,
            "current_monthly_loss_pct": monthly_loss_pct,
            "limit": self.max_monthly_loss_pct,
            "monthly_pnl": self.monthly_pnl,
            "month_start_value": self.month_start_portfolio_value,
            "message": f"Monthly loss {monthly_loss_pct:.1%} exceeds limit {self.max_monthly_loss_pct:.1%}"
        }
    
    def _check_daily_loss_limit(self, current_portfolio_value: float) -> Dict[str, Any]:
        """Check if daily loss limit is breached"""
        daily_loss_pct = abs(self.daily_pnl) / self.day_start_portfolio_value
        
        return {
            "triggered": daily_loss_pct > self.max_daily_loss_pct,
            "current_daily_loss_pct": daily_loss_pct,
            "limit": self.max_daily_loss_pct,
            "daily_pnl": self.daily_pnl,
            "day_start_value": self.day_start_portfolio_value,
            "message": f"Daily loss {daily_loss_pct:.1%} exceeds limit {self.max_daily_loss_pct:.1%}"
        }
    
    def _check_vix_spike_condition(self, current_vix: float, 
                                 active_positions: List[Dict]) -> Dict[str, Any]:
        """Check for VIX spike with short volatility positions"""
        short_vol_positions = [
            pos for pos in active_positions 
            if pos.get("strategy") == "SHORT_VOLATILITY"
        ]
        
        vix_spike_triggered = (
            current_vix > self.vix_spike_threshold and 
            len(short_vol_positions) > 0
        )
        
        return {
            "triggered": vix_spike_triggered,
            "current_vix": current_vix,
            "threshold": self.vix_spike_threshold,
            "short_vol_positions": len(short_vol_positions),
            "message": f"VIX spike to {current_vix:.1f} with {len(short_vol_positions)} short vol positions"
        }
    
    def _check_correlation_breakdown(self, market_data: Dict) -> Dict[str, Any]:
        """Check for correlation breakdown (all assets moving together)"""
        # This would require actual market correlation data
        # For now, simulate based on VIX and market stress indicators
        
        # Placeholder implementation
        correlation_breakdown = False
        
        # In practice, you'd calculate correlations between:
        # - Nifty sectors
        # - Indian vs global markets
        # - Equity vs bond correlations
        
        return {
            "triggered": correlation_breakdown,
            "correlation_level": 0.5,  # Placeholder
            "threshold": self.correlation_breakdown_threshold,
            "message": "Correlation analysis requires additional market data"
        }
    
    def _check_portfolio_greeks_explosion(self, active_positions: List[Dict]) -> Dict[str, Any]:
        """Check for dangerous portfolio Greeks levels"""
        if not active_positions:
            return {"triggered": False}
        
        # Calculate total portfolio Greeks (simplified)
        total_delta = sum(pos.get("delta", 0) for pos in active_positions)
        total_gamma = sum(pos.get("gamma", 0) for pos in active_positions)
        total_vega = sum(pos.get("vega", 0) for pos in active_positions)
        
        # Define dangerous levels (as percentage of portfolio)
        dangerous_delta = abs(total_delta) > (self.portfolio_size * 0.2)  # 20% delta exposure
        dangerous_gamma = abs(total_gamma) > (self.portfolio_size * 0.05)  # 5% gamma exposure
        dangerous_vega = abs(total_vega) > (self.portfolio_size * 0.1)   # 10% vega exposure
        
        greeks_explosion = dangerous_delta or dangerous_gamma or dangerous_vega
        
        return {
            "triggered": greeks_explosion,
            "total_delta": total_delta,
            "total_gamma": total_gamma,
            "total_vega": total_vega,
            "dangerous_delta": dangerous_delta,
            "dangerous_gamma": dangerous_gamma,
            "dangerous_vega": dangerous_vega,
            "message": f"Portfolio Greeks: Delta={total_delta:.0f}, Gamma={total_gamma:.0f}, Vega={total_vega:.0f}"
        }
    
    def _check_liquidity_crisis(self, active_positions: List[Dict]) -> Dict[str, Any]:
        """Check for liquidity crisis in positions"""
        liquidity_issues = 0
        total_positions = len(active_positions)
        
        for position in active_positions:
            # Check if position has liquidity issues
            # This would require real-time bid-ask spread monitoring
            # For now, use simplified checks
            
            legs = position.get("legs", [])
            for leg in legs:
                # Placeholder liquidity check
                # In practice, check bid-ask spreads, volume, open interest
                pass
        
        liquidity_crisis = liquidity_issues > (total_positions * 0.5)  # 50% of positions illiquid
        
        return {
            "triggered": liquidity_crisis,
            "positions_with_liquidity_issues": liquidity_issues,
            "total_positions": total_positions,
            "liquidity_ratio": liquidity_issues / max(1, total_positions),
            "message": f"{liquidity_issues}/{total_positions} positions have liquidity issues"
        }
    
    def _check_warning_conditions(self, current_portfolio_value: float,
                                current_vix: float,
                                active_positions: List[Dict]) -> List[str]:
        """Check for warning conditions that aren't catastrophic yet"""
        warnings = []
        
        # Monthly loss approaching limit
        monthly_loss_pct = abs(self.monthly_pnl) / self.month_start_portfolio_value
        if monthly_loss_pct > self.max_monthly_loss_pct * 0.7:  # 70% of limit
            warnings.append(f"Monthly loss {monthly_loss_pct:.1%} approaching limit")
        
        # Daily loss approaching limit
        daily_loss_pct = abs(self.daily_pnl) / self.day_start_portfolio_value
        if daily_loss_pct > self.max_daily_loss_pct * 0.7:  # 70% of limit
            warnings.append(f"Daily loss {daily_loss_pct:.1%} approaching limit")
        
        # VIX approaching spike threshold with short vol
        short_vol_count = len([p for p in active_positions if p.get("strategy") == "SHORT_VOLATILITY"])
        if current_vix > self.vix_spike_threshold * 0.8 and short_vol_count > 0:
            warnings.append(f"VIX {current_vix:.1f} approaching spike threshold with short vol positions")
        
        # Too many concurrent positions
        if len(active_positions) > 5:
            warnings.append(f"High number of concurrent positions: {len(active_positions)}")
        
        return warnings
    
    def execute_emergency_liquidation(self, active_positions: List[Dict],
                                    failure_type: str) -> Dict[str, Any]:
        """
        Execute emergency liquidation of all positions
        The Architect: This is the nuclear option - use it to save what's left
        """
        liquidation_result = {
            "liquidation_executed": True,
            "failure_type": failure_type,
            "positions_liquidated": len(active_positions),
            "liquidation_time": datetime.now().isoformat(),
            "estimated_liquidation_cost": 0.0,
            "liquidated_positions": []
        }
        
        # Log the catastrophic event
        catastrophic_event = {
            "timestamp": datetime.now().isoformat(),
            "failure_type": failure_type,
            "portfolio_value_at_failure": self.portfolio_size + self.monthly_pnl,
            "monthly_pnl": self.monthly_pnl,
            "daily_pnl": self.daily_pnl,
            "positions_count": len(active_positions)
        }
        
        self.failure_events.append(catastrophic_event)
        
        # In a real system, this would:
        # 1. Send emergency orders to close all positions
        # 2. Cancel all pending orders
        # 3. Notify risk management
        # 4. Generate incident report
        
        logger.critical(f"EMERGENCY LIQUIDATION EXECUTED: {failure_type}")
        logger.critical(f"Positions liquidated: {len(active_positions)}")
        logger.critical(f"Portfolio value: ₹{self.portfolio_size + self.monthly_pnl:,.2f}")
        
        return liquidation_result
    
    def get_failure_statistics(self) -> Dict[str, Any]:
        """Get statistics on failure events and system performance"""
        return {
            "total_failure_events": len(self.failure_events),
            "emergency_liquidations": len(self.emergency_liquidations),
            "current_monthly_pnl": self.monthly_pnl,
            "current_daily_pnl": self.daily_pnl,
            "monthly_loss_limit_utilization": abs(self.monthly_pnl) / (self.portfolio_size * self.max_monthly_loss_pct),
            "daily_loss_limit_utilization": abs(self.daily_pnl) / (self.portfolio_size * self.max_daily_loss_pct),
            "failure_events_history": self.failure_events[-10:],  # Last 10 events
            "system_status": "OPERATIONAL" if len(self.failure_events) == 0 else "DEGRADED"
        }
