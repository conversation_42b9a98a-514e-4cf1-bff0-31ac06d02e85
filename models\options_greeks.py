"""
Options Greeks Calculator for Indian Markets
The Architect's implementation - Black-Scholes with Indian risk-free rate
"""
import numpy as np
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from scipy.stats import norm
import logging

logger = logging.getLogger(__name__)

class OptionsGreeksCalculator:
    """
    Black-Scholes options pricing and Greeks calculation for NSE options
    The Architect: This is where we calculate real risk, not fantasy numbers
    """
    
    def __init__(self, risk_free_rate: float = 0.065):
        """
        Initialize with Indian risk-free rate
        The Architect: Use RBI repo rate + spread, currently ~6.5%
        """
        self.risk_free_rate = risk_free_rate
        
    def _time_to_expiry(self, expiry_date: date, current_date: Optional[date] = None) -> float:
        """Calculate time to expiry in years"""
        if current_date is None:
            current_date = datetime.now().date()
        
        days_to_expiry = (expiry_date - current_date).days
        
        # The Architect: Account for weekends and holidays
        # Rough approximation: 252 trading days per year
        return max(0.001, days_to_expiry / 365.0)  # Minimum 1 day
    
    def _d1_d2(self, S: float, K: float, T: float, r: float, sigma: float) -> Tuple[float, float]:
        """Calculate d1 and d2 for Black-Scholes"""
        if T <= 0 or sigma <= 0:
            return 0.0, 0.0
            
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        return d1, d2
    
    def calculate_option_price(self, 
                             spot_price: float,
                             strike_price: float,
                             time_to_expiry: float,
                             volatility: float,
                             option_type: str = "CE") -> float:
        """
        Calculate Black-Scholes option price
        The Architect: This is theoretical - market price is what matters
        """
        if time_to_expiry <= 0 or volatility <= 0:
            return max(0, spot_price - strike_price) if option_type == "CE" else max(0, strike_price - spot_price)
        
        d1, d2 = self._d1_d2(spot_price, strike_price, time_to_expiry, self.risk_free_rate, volatility)
        
        if option_type == "CE":  # Call option
            price = (spot_price * norm.cdf(d1) - 
                    strike_price * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(d2))
        else:  # Put option
            price = (strike_price * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(-d2) - 
                    spot_price * norm.cdf(-d1))
        
        return max(0, price)
    
    def calculate_delta(self,
                       spot_price: float,
                       strike_price: float,
                       time_to_expiry: float,
                       volatility: float,
                       option_type: str = "CE") -> float:
        """
        Calculate option delta
        The Architect: This tells us directional exposure per ₹1 move in Nifty
        """
        if time_to_expiry <= 0:
            if option_type == "CE":
                return 1.0 if spot_price > strike_price else 0.0
            else:
                return -1.0 if spot_price < strike_price else 0.0
        
        d1, _ = self._d1_d2(spot_price, strike_price, time_to_expiry, self.risk_free_rate, volatility)
        
        if option_type == "CE":
            return norm.cdf(d1)
        else:
            return norm.cdf(d1) - 1.0
    
    def calculate_gamma(self,
                       spot_price: float,
                       strike_price: float,
                       time_to_expiry: float,
                       volatility: float) -> float:
        """
        Calculate option gamma
        The Architect: This is acceleration risk - how fast delta changes
        """
        if time_to_expiry <= 0 or volatility <= 0:
            return 0.0
        
        d1, _ = self._d1_d2(spot_price, strike_price, time_to_expiry, self.risk_free_rate, volatility)
        
        gamma = norm.pdf(d1) / (spot_price * volatility * np.sqrt(time_to_expiry))
        return gamma
    
    def calculate_theta(self,
                       spot_price: float,
                       strike_price: float,
                       time_to_expiry: float,
                       volatility: float,
                       option_type: str = "CE") -> float:
        """
        Calculate option theta (time decay)
        The Architect: This is how much you lose per day holding options
        """
        if time_to_expiry <= 0:
            return 0.0
        
        d1, d2 = self._d1_d2(spot_price, strike_price, time_to_expiry, self.risk_free_rate, volatility)
        
        if option_type == "CE":
            theta = (-(spot_price * norm.pdf(d1) * volatility) / (2 * np.sqrt(time_to_expiry)) -
                    self.risk_free_rate * strike_price * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(d2))
        else:
            theta = (-(spot_price * norm.pdf(d1) * volatility) / (2 * np.sqrt(time_to_expiry)) +
                    self.risk_free_rate * strike_price * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(-d2))
        
        return theta / 365.0  # Per day
    
    def calculate_vega(self,
                      spot_price: float,
                      strike_price: float,
                      time_to_expiry: float,
                      volatility: float) -> float:
        """
        Calculate option vega
        The Architect: This is volatility sensitivity - our edge in vol trading
        """
        if time_to_expiry <= 0:
            return 0.0
        
        d1, _ = self._d1_d2(spot_price, strike_price, time_to_expiry, self.risk_free_rate, volatility)
        
        vega = spot_price * norm.pdf(d1) * np.sqrt(time_to_expiry)
        return vega / 100.0  # Per 1% change in volatility
    
    def calculate_all_greeks(self,
                           spot_price: float,
                           strike_price: float,
                           expiry_date: date,
                           volatility: float,
                           option_type: str = "CE",
                           current_date: Optional[date] = None) -> Dict[str, float]:
        """
        Calculate all Greeks for an option
        The Architect: This is your risk dashboard
        """
        time_to_expiry = self._time_to_expiry(expiry_date, current_date)
        
        # Calculate theoretical price
        theoretical_price = self.calculate_option_price(
            spot_price, strike_price, time_to_expiry, volatility, option_type
        )
        
        # Calculate all Greeks
        delta = self.calculate_delta(
            spot_price, strike_price, time_to_expiry, volatility, option_type
        )
        
        gamma = self.calculate_gamma(
            spot_price, strike_price, time_to_expiry, volatility
        )
        
        theta = self.calculate_theta(
            spot_price, strike_price, time_to_expiry, volatility, option_type
        )
        
        vega = self.calculate_vega(
            spot_price, strike_price, time_to_expiry, volatility
        )
        
        # Calculate intrinsic and time value
        if option_type == "CE":
            intrinsic_value = max(0, spot_price - strike_price)
        else:
            intrinsic_value = max(0, strike_price - spot_price)
        
        time_value = theoretical_price - intrinsic_value
        
        return {
            "theoretical_price": round(theoretical_price, 2),
            "intrinsic_value": round(intrinsic_value, 2),
            "time_value": round(time_value, 2),
            "delta": round(delta, 4),
            "gamma": round(gamma, 6),
            "theta": round(theta, 2),
            "vega": round(vega, 2),
            "time_to_expiry": round(time_to_expiry, 4),
            "moneyness": round(spot_price / strike_price, 4)
        }
    
    def calculate_portfolio_greeks(self, positions: List[Dict]) -> Dict[str, float]:
        """
        Calculate portfolio-level Greeks
        The Architect: This is how you manage overall risk
        
        positions format:
        [
            {
                "quantity": 100,  # Number of contracts
                "spot_price": 19500,
                "strike_price": 19500,
                "expiry_date": date(2024, 1, 25),
                "volatility": 0.15,
                "option_type": "CE"
            }
        ]
        """
        portfolio_greeks = {
            "total_delta": 0.0,
            "total_gamma": 0.0,
            "total_theta": 0.0,
            "total_vega": 0.0,
            "total_value": 0.0
        }
        
        for position in positions:
            greeks = self.calculate_all_greeks(
                position["spot_price"],
                position["strike_price"],
                position["expiry_date"],
                position["volatility"],
                position["option_type"]
            )
            
            quantity = position["quantity"]
            
            # Aggregate Greeks (multiply by quantity)
            portfolio_greeks["total_delta"] += greeks["delta"] * quantity
            portfolio_greeks["total_gamma"] += greeks["gamma"] * quantity
            portfolio_greeks["total_theta"] += greeks["theta"] * quantity
            portfolio_greeks["total_vega"] += greeks["vega"] * quantity
            portfolio_greeks["total_value"] += greeks["theoretical_price"] * quantity
        
        # Round results
        for key in portfolio_greeks:
            portfolio_greeks[key] = round(portfolio_greeks[key], 2)
        
        return portfolio_greeks
    
    def calculate_implied_volatility(self,
                                   market_price: float,
                                   spot_price: float,
                                   strike_price: float,
                                   time_to_expiry: float,
                                   option_type: str = "CE",
                                   max_iterations: int = 100) -> float:
        """
        Calculate implied volatility using Newton-Raphson method
        The Architect: This is what the market is actually pricing
        """
        if time_to_expiry <= 0:
            return 0.0
        
        # Initial guess
        sigma = 0.2
        
        for i in range(max_iterations):
            price = self.calculate_option_price(
                spot_price, strike_price, time_to_expiry, sigma, option_type
            )
            
            vega = self.calculate_vega(
                spot_price, strike_price, time_to_expiry, sigma
            ) * 100  # Convert back to per unit
            
            if abs(price - market_price) < 0.01 or vega < 0.001:
                break
            
            # Newton-Raphson update
            sigma = sigma - (price - market_price) / vega
            sigma = max(0.001, min(5.0, sigma))  # Keep within reasonable bounds
        
        return sigma
