"""
Global Correlation Monitoring System
The Architect's implementation - track cross-asset correlations and volatility spillovers
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class CorrelationAlert:
    """Correlation alert data"""
    asset_pair: str
    correlation: float
    threshold: float
    alert_type: str
    severity: str
    timestamp: datetime
    recommendation: str

@dataclass
class VolatilitySpillover:
    """Volatility spillover detection"""
    source_asset: str
    target_asset: str
    spillover_strength: float
    lag_days: int
    confidence: float
    direction: str  # "positive" or "negative"

class GlobalCorrelationMonitor:
    """
    The Architect's Global Correlation Monitoring System
    
    Core Logic:
    - Track SPX, VIX, DXY correlations with Indian markets
    - When correlation >0.7, reduce position count to 1
    - Detect volatility spillovers across markets
    - Real-time correlation regime detection
    - Cross-asset risk management alerts
    """
    
    def __init__(self, lookback_days: int = 60):
        self.lookback_days = lookback_days
        
        # Asset symbols for monitoring
        self.global_assets = {
            'SPX': '^GSPC',      # S&P 500
            'VIX': '^VIX',       # VIX
            'DXY': 'DX-Y.NYB',   # Dollar Index
            'NIFTY': '^NSEI',    # Nifty 50
            'INDIA_VIX': '^INDIAVIX'  # India VIX
        }
        
        # Correlation thresholds
        self.high_correlation_threshold = 0.7
        self.extreme_correlation_threshold = 0.85
        self.spillover_threshold = 0.6
        
        # Position limits based on correlation
        self.correlation_position_limits = {
            0.7: 1,   # Max 1 position when correlation > 70%
            0.6: 2,   # Max 2 positions when correlation > 60%
            0.5: 3,   # Max 3 positions when correlation > 50%
        }
        
        # Data cache
        self.price_data: Dict[str, pd.DataFrame] = {}
        self.correlation_matrix = None
        self.last_update = None
        
        # Alert history
        self.correlation_alerts: List[CorrelationAlert] = []
        self.spillover_events: List[VolatilitySpillover] = []
    
    async def update_market_data(self) -> bool:
        """
        Update market data for all tracked assets
        The Architect: Fresh data is the foundation of good decisions
        """
        try:
            # For testing purposes, generate synthetic data
            # In production, this would fetch real market data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.lookback_days + 10)

            dates = pd.date_range(start=start_date, end=end_date, freq='D')

            for asset_name, symbol in self.global_assets.items():
                try:
                    # Generate synthetic price data for testing
                    n_days = len(dates)

                    if asset_name == 'VIX' or asset_name == 'INDIA_VIX':
                        # VIX-like data (mean reverting, positive)
                        base_level = 18 if asset_name == 'VIX' else 16
                        prices = base_level + 5 * np.sin(np.arange(n_days) * 0.1) + np.random.normal(0, 2, n_days)
                        prices = np.maximum(prices, 8)  # VIX can't go below ~8
                    else:
                        # Stock index data
                        base_price = 4000 if asset_name == 'SPX' else 19000 if asset_name == 'NIFTY' else 100
                        returns = np.random.normal(0.0005, 0.02, n_days)  # Daily returns
                        prices = base_price * np.cumprod(1 + returns)

                    data = pd.DataFrame({
                        'Close': prices,
                        'returns': np.concatenate([[0], np.diff(prices) / prices[:-1]]),
                        'log_returns': np.concatenate([[0], np.diff(np.log(prices))]),
                    }, index=dates)

                    # Calculate rolling volatility
                    data['volatility'] = data['returns'].rolling(20).std() * np.sqrt(252)

                    self.price_data[asset_name] = data.tail(self.lookback_days)
                    logger.info(f"Generated synthetic data for {asset_name}: {len(data)} records")

                except Exception as e:
                    logger.error(f"Failed to generate data for {asset_name}: {e}")

            self.last_update = datetime.now()
            return True

        except Exception as e:
            logger.error(f"Failed to update market data: {e}")
            return False
    
    def calculate_correlation_matrix(self, return_type: str = 'returns') -> np.ndarray:
        """
        Calculate correlation matrix for all assets
        The Architect: Know how everything moves together
        """
        if not self.price_data:
            logger.warning("No price data available for correlation calculation")
            return np.eye(len(self.global_assets))
        
        # Prepare return data
        return_data = {}
        
        for asset_name, data in self.price_data.items():
            if return_type in data.columns and not data[return_type].empty:
                return_data[asset_name] = data[return_type].dropna()
        
        if len(return_data) < 2:
            logger.warning("Insufficient return data for correlation calculation")
            return np.eye(len(self.global_assets))
        
        # Align data by dates
        df = pd.DataFrame(return_data)
        df = df.dropna()
        
        if df.empty:
            logger.warning("No overlapping data for correlation calculation")
            return np.eye(len(self.global_assets))
        
        # Calculate correlation matrix
        self.correlation_matrix = df.corr().values
        
        logger.info(f"Calculated correlation matrix from {len(df)} observations")
        return self.correlation_matrix
    
    def detect_high_correlations(self) -> List[CorrelationAlert]:
        """
        Detect high correlations that require position adjustments
        The Architect: When everything moves together, reduce your bets
        """
        alerts = []
        
        if self.correlation_matrix is None:
            self.calculate_correlation_matrix()
        
        if self.correlation_matrix is None:
            return alerts
        
        asset_names = list(self.global_assets.keys())
        
        # Check all asset pairs
        for i in range(len(asset_names)):
            for j in range(i + 1, len(asset_names)):
                correlation = self.correlation_matrix[i, j]
                asset_pair = f"{asset_names[i]}-{asset_names[j]}"
                
                # Check for high correlations
                if abs(correlation) >= self.extreme_correlation_threshold:
                    severity = "CRITICAL"
                    recommendation = "Reduce to 1 position maximum"
                elif abs(correlation) >= self.high_correlation_threshold:
                    severity = "HIGH"
                    recommendation = "Limit position count"
                else:
                    continue
                
                alert = CorrelationAlert(
                    asset_pair=asset_pair,
                    correlation=correlation,
                    threshold=self.high_correlation_threshold,
                    alert_type="HIGH_CORRELATION",
                    severity=severity,
                    timestamp=datetime.now(),
                    recommendation=recommendation
                )
                
                alerts.append(alert)
        
        # Store alerts
        self.correlation_alerts.extend(alerts)
        
        # Keep only recent alerts (last 100)
        if len(self.correlation_alerts) > 100:
            self.correlation_alerts = self.correlation_alerts[-100:]
        
        return alerts
    
    def detect_volatility_spillovers(self) -> List[VolatilitySpillover]:
        """
        Detect volatility spillovers between markets
        The Architect: Volatility is contagious - know where it's spreading
        """
        spillovers = []
        
        if not self.price_data:
            return spillovers
        
        # Focus on key spillover relationships
        spillover_pairs = [
            ('VIX', 'INDIA_VIX'),
            ('SPX', 'NIFTY'),
            ('DXY', 'NIFTY'),
            ('VIX', 'NIFTY')
        ]
        
        for source, target in spillover_pairs:
            if source not in self.price_data or target not in self.price_data:
                continue
            
            spillover = self._calculate_spillover(source, target)
            if spillover and spillover.spillover_strength >= self.spillover_threshold:
                spillovers.append(spillover)
        
        # Store spillover events
        self.spillover_events.extend(spillovers)
        
        # Keep only recent events (last 50)
        if len(self.spillover_events) > 50:
            self.spillover_events = self.spillover_events[-50:]
        
        return spillovers
    
    def _calculate_spillover(self, source_asset: str, target_asset: str) -> Optional[VolatilitySpillover]:
        """Calculate volatility spillover between two assets"""
        try:
            source_data = self.price_data[source_asset]['volatility'].dropna()
            target_data = self.price_data[target_asset]['volatility'].dropna()
            
            # Align data
            aligned_data = pd.DataFrame({
                'source': source_data,
                'target': target_data
            }).dropna()
            
            if len(aligned_data) < 20:  # Need minimum data
                return None
            
            # Test different lags (0-5 days)
            best_correlation = 0
            best_lag = 0
            
            for lag in range(6):
                if lag == 0:
                    source_series = aligned_data['source']
                    target_series = aligned_data['target']
                else:
                    source_series = aligned_data['source'].shift(lag)
                    target_series = aligned_data['target']
                    
                # Remove NaN values
                valid_data = pd.DataFrame({
                    'source': source_series,
                    'target': target_series
                }).dropna()
                
                if len(valid_data) < 15:
                    continue
                
                correlation, p_value = pearsonr(valid_data['source'], valid_data['target'])
                
                if abs(correlation) > abs(best_correlation) and p_value < 0.05:
                    best_correlation = correlation
                    best_lag = lag
            
            if abs(best_correlation) < self.spillover_threshold:
                return None
            
            # Calculate confidence based on correlation strength and sample size
            confidence = min(0.95, abs(best_correlation) * (len(aligned_data) / 60))
            
            direction = "positive" if best_correlation > 0 else "negative"
            
            return VolatilitySpillover(
                source_asset=source_asset,
                target_asset=target_asset,
                spillover_strength=abs(best_correlation),
                lag_days=best_lag,
                confidence=confidence,
                direction=direction
            )
            
        except Exception as e:
            logger.error(f"Error calculating spillover {source_asset}->{target_asset}: {e}")
            return None
    
    def get_position_limits(self) -> Dict[str, Any]:
        """
        Get position limits based on current correlations
        The Architect: Adjust your risk when correlations spike
        """
        if self.correlation_matrix is None:
            self.calculate_correlation_matrix()
        
        # Find maximum correlation (excluding diagonal)
        if self.correlation_matrix is not None:
            np.fill_diagonal(self.correlation_matrix, 0)  # Exclude self-correlation
            max_correlation = np.max(np.abs(self.correlation_matrix))
        else:
            max_correlation = 0.3  # Default assumption
        
        # Determine position limits
        max_positions = 5  # Default
        
        for threshold, limit in sorted(self.correlation_position_limits.items(), reverse=True):
            if max_correlation >= threshold:
                max_positions = limit
                break
        
        return {
            "max_positions": max_positions,
            "max_correlation": max_correlation,
            "correlation_regime": self._classify_correlation_regime(max_correlation),
            "position_size_multiplier": self._get_correlation_size_multiplier(max_correlation),
            "reasoning": f"Max correlation {max_correlation:.2f} -> limit {max_positions} positions"
        }
    
    def _classify_correlation_regime(self, max_correlation: float) -> str:
        """Classify correlation regime"""
        if max_correlation >= 0.85:
            return "EXTREME_CORRELATION"
        elif max_correlation >= 0.7:
            return "HIGH_CORRELATION"
        elif max_correlation >= 0.5:
            return "MODERATE_CORRELATION"
        else:
            return "LOW_CORRELATION"
    
    def _get_correlation_size_multiplier(self, max_correlation: float) -> float:
        """Get position size multiplier based on correlation"""
        if max_correlation >= 0.85:
            return 0.3  # 70% reduction
        elif max_correlation >= 0.7:
            return 0.5  # 50% reduction
        elif max_correlation >= 0.6:
            return 0.7  # 30% reduction
        else:
            return 1.0  # No reduction
    
    def get_correlation_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive correlation dashboard"""
        # Update correlations
        self.calculate_correlation_matrix()
        
        # Get recent alerts
        recent_alerts = [alert for alert in self.correlation_alerts 
                        if (datetime.now() - alert.timestamp).days <= 1]
        
        # Get recent spillovers
        recent_spillovers = [spillover for spillover in self.spillover_events 
                           if len(self.spillover_events) > 0][-5:]  # Last 5
        
        # Position limits
        position_limits = self.get_position_limits()
        
        return {
            "correlation_matrix": self.correlation_matrix.tolist() if self.correlation_matrix is not None else None,
            "asset_names": list(self.global_assets.keys()),
            "position_limits": position_limits,
            "recent_alerts": [
                {
                    "asset_pair": alert.asset_pair,
                    "correlation": alert.correlation,
                    "severity": alert.severity,
                    "recommendation": alert.recommendation
                } for alert in recent_alerts
            ],
            "volatility_spillovers": [
                {
                    "source": spillover.source_asset,
                    "target": spillover.target_asset,
                    "strength": spillover.spillover_strength,
                    "lag_days": spillover.lag_days,
                    "direction": spillover.direction
                } for spillover in recent_spillovers
            ],
            "data_status": {
                "last_update": self.last_update.isoformat() if self.last_update else None,
                "assets_tracked": len(self.price_data),
                "data_quality": "Good" if len(self.price_data) >= 4 else "Limited"
            }
        }
    
    def should_reduce_positions(self) -> Tuple[bool, str]:
        """Check if positions should be reduced due to correlations"""
        position_limits = self.get_position_limits()
        
        if position_limits["max_positions"] <= 1:
            return True, f"High correlation detected: {position_limits['reasoning']}"
        
        # Check for recent spillover events
        recent_spillovers = [s for s in self.spillover_events 
                           if s.spillover_strength >= 0.8]  # Very strong spillovers
        
        if recent_spillovers:
            return True, f"Strong volatility spillover detected: {recent_spillovers[0].source_asset}->{recent_spillovers[0].target_asset}"
        
        return False, "Correlation levels acceptable"
    
    def get_risk_warnings(self) -> List[str]:
        """Get current risk warnings based on correlations"""
        warnings = []
        
        position_limits = self.get_position_limits()
        
        if position_limits["correlation_regime"] == "EXTREME_CORRELATION":
            warnings.append("CRITICAL: Extreme correlation detected - maximum 1 position allowed")
        elif position_limits["correlation_regime"] == "HIGH_CORRELATION":
            warnings.append("WARNING: High correlation detected - reduce position count")
        
        # Check for spillover warnings
        strong_spillovers = [s for s in self.spillover_events 
                           if s.spillover_strength >= 0.8 and s.confidence >= 0.7]
        
        for spillover in strong_spillovers[-3:]:  # Last 3 strong spillovers
            warnings.append(f"Volatility spillover: {spillover.source_asset} -> {spillover.target_asset} "
                          f"(strength: {spillover.spillover_strength:.2f})")
        
        return warnings
