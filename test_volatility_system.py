"""
Comprehensive Test Suite for The Architect's Volatility Trading System
This validates every component works as designed
"""
import asyncio
import logging
import pytest
from datetime import datetime, date, timedelta
import numpy as np

from data_collectors.vix_collector import VIXDataCollector
from models.options_greeks import OptionsGreeksCalculator
from strategies.volatility_mean_reversion import VolatilityMeanReversionStrategy
from risk_management.volatility_risk_manager import VolatilityRiskManager
from paper_trading.volatility_paper_trader import VolatilityPaperTrader
from main_volatility_system import VolatilityTradingSystem

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestVolatilitySystem:
    """
    The Architect's Test Suite - validate everything works
    """
    
    def setup_method(self):
        """Setup for each test"""
        self.portfolio_size = 1000000  # 10 lakh
        self.vix_collector = VIXDataCollector()
        self.greeks_calculator = OptionsGreeksCalculator()
        self.strategy = VolatilityMeanReversionStrategy(self.portfolio_size)
        self.risk_manager = VolatilityRiskManager(self.portfolio_size)
        self.paper_trader = VolatilityPaperTrader(self.portfolio_size)
    
    async def test_vix_data_collection(self):
        """Test VIX data collection and signal generation"""
        logger.info("Testing VIX data collection...")
        
        async with self.vix_collector as vix:
            # Test current VIX data
            current_vix = await vix.get_current_vix()
            assert "vix" in current_vix
            assert current_vix["vix"] > 0
            assert "timestamp" in current_vix
            
            # Test historical VIX data
            historical_vix = await vix.get_vix_historical(30)
            assert len(historical_vix) > 20
            assert "vix" in historical_vix.columns
            assert "vix_ma_10" in historical_vix.columns
            
            # Test signal generation
            signals = vix.calculate_vix_signals(historical_vix)
            assert "signal" in signals
            assert "confidence" in signals
            assert signals["confidence"] >= 0
            
        logger.info("✓ VIX data collection tests passed")
    
    def test_options_greeks_calculation(self):
        """Test options Greeks calculations"""
        logger.info("Testing options Greeks calculations...")
        
        # Test parameters
        spot_price = 19500
        strike_price = 19500
        expiry_date = date.today() + timedelta(days=14)
        volatility = 0.15
        
        # Test all Greeks calculation
        greeks = self.greeks_calculator.calculate_all_greeks(
            spot_price, strike_price, expiry_date, volatility, "CE"
        )
        
        # Validate Greeks
        assert "theoretical_price" in greeks
        assert "delta" in greeks
        assert "gamma" in greeks
        assert "theta" in greeks
        assert "vega" in greeks
        
        # ATM call delta should be around 0.5
        assert 0.4 < greeks["delta"] < 0.6
        
        # Gamma should be positive
        assert greeks["gamma"] > 0
        
        # Theta should be negative for long options
        assert greeks["theta"] < 0
        
        # Vega should be positive
        assert greeks["vega"] > 0
        
        # Test portfolio Greeks
        positions = [
            {
                "quantity": 75,  # 1 lot
                "spot_price": spot_price,
                "strike_price": strike_price,
                "expiry_date": expiry_date,
                "volatility": volatility,
                "option_type": "CE"
            }
        ]
        
        portfolio_greeks = self.greeks_calculator.calculate_portfolio_greeks(positions)
        assert "total_delta" in portfolio_greeks
        assert portfolio_greeks["total_delta"] > 0
        
        logger.info("✓ Options Greeks calculation tests passed")
    
    def test_risk_management(self):
        """Test risk management framework"""
        logger.info("Testing risk management...")
        
        # Test position validation
        test_signal = {
            "strategy": "LONG_VOLATILITY",
            "max_loss": 5000,  # ₹5,000 max loss
            "options": [],
            "lots": 1
        }
        
        is_valid, message = self.risk_manager.validate_new_position(test_signal)
        assert is_valid, f"Valid position rejected: {message}"
        
        # Test position size calculation
        position_size = self.risk_manager.calculate_position_size(test_signal)
        assert position_size > 0
        
        # Test risk limits
        # Simulate large loss
        self.risk_manager.update_daily_pnl(-60000)  # ₹60,000 loss
        
        should_halt, reason = self.risk_manager.should_halt_trading()
        assert should_halt, "Risk limits not triggered"
        
        # Reset for next tests
        self.risk_manager.daily_pnl = 0
        
        logger.info("✓ Risk management tests passed")
    
    async def test_paper_trading(self):
        """Test paper trading execution"""
        logger.info("Testing paper trading...")
        
        # Create mock options data
        from models.data_models import OptionsData
        
        mock_options = [
            OptionsData(
                symbol="NIFTY",
                expiry_date=datetime.now() + timedelta(days=14),
                strike=19500,
                option_type="CE",
                last_price=100.0,
                bid_price=98.0,
                ask_price=102.0,
                volume=1000,
                open_interest=5000,
                bid_qty=100,
                ask_qty=100,
                implied_volatility=15.0,
                timestamp=datetime.now()
            ),
            OptionsData(
                symbol="NIFTY",
                expiry_date=datetime.now() + timedelta(days=14),
                strike=19500,
                option_type="PE",
                last_price=95.0,
                bid_price=93.0,
                ask_price=97.0,
                volume=1000,
                open_interest=5000,
                bid_qty=100,
                ask_qty=100,
                implied_volatility=15.0,
                timestamp=datetime.now()
            )
        ]
        
        # Create test signal
        test_signal = {
            "strategy": "LONG_VOLATILITY",
            "options": mock_options,
            "lots": 1,
            "profit_target": 200.0,
            "stop_loss": 100.0,
            "confidence": 0.8,
            "vix_data": {"current_vix": 11.5}
        }
        
        # Test trade execution
        initial_cash = self.paper_trader.available_cash
        position = await self.paper_trader.execute_volatility_trade(test_signal)
        
        assert position is not None
        assert position["strategy"] == "LONG_VOLATILITY"
        assert len(position["legs"]) == 2
        assert self.paper_trader.available_cash < initial_cash
        
        # Test position update
        mock_prices = {
            (19500, mock_options[0].expiry_date, "CE"): 110.0,
            (19500, mock_options[1].expiry_date, "PE"): 105.0
        }
        
        await self.paper_trader.update_positions(mock_prices)
        
        # Check if position was updated
        position_id = position["id"]
        updated_position = self.paper_trader.active_positions[position_id]
        assert "unrealized_pnl" in updated_position
        
        # Test position closing
        closed_position = await self.paper_trader.close_position(position_id, "Test close")
        assert closed_position is not None
        assert closed_position["status"] == "CLOSED"
        assert "final_pnl" in closed_position
        
        logger.info("✓ Paper trading tests passed")
    
    def test_transaction_costs(self):
        """Test realistic transaction cost calculations"""
        logger.info("Testing transaction costs...")
        
        # Test cost calculation
        premium = 100.0  # ₹100 per option
        lots = 2
        
        costs = self.paper_trader.calculate_transaction_costs(premium, lots)
        
        # Should include brokerage, STT, transaction charges, GST
        assert costs > 0
        
        # For 2 lots, should be reasonable
        assert 50 < costs < 200  # Between ₹50-200
        
        logger.info(f"Transaction costs for 2 lots: ₹{costs:.2f}")
        logger.info("✓ Transaction cost tests passed")
    
    def test_slippage_modeling(self):
        """Test slippage application"""
        logger.info("Testing slippage modeling...")
        
        theoretical_price = 100.0
        lots = 1
        
        # Test buy slippage
        buy_price = self.paper_trader.apply_slippage(theoretical_price, "BUY", lots)
        assert buy_price > theoretical_price  # Should be higher for buys
        
        # Test sell slippage
        sell_price = self.paper_trader.apply_slippage(theoretical_price, "SELL", lots)
        assert sell_price < theoretical_price  # Should be lower for sells
        
        # Test larger order impact
        large_lots = 10
        large_buy_price = self.paper_trader.apply_slippage(theoretical_price, "BUY", large_lots)
        assert large_buy_price > buy_price  # Larger orders should have more slippage
        
        logger.info(f"Buy slippage: ₹{buy_price - theoretical_price:.2f}")
        logger.info(f"Sell slippage: ₹{theoretical_price - sell_price:.2f}")
        logger.info("✓ Slippage modeling tests passed")
    
    async def test_complete_system_integration(self):
        """Test complete system integration"""
        logger.info("Testing complete system integration...")
        
        # Initialize system
        system = VolatilityTradingSystem(portfolio_size=1000000)
        
        # Test system initialization
        assert system.portfolio_size == 1000000
        assert system.is_running == False
        
        # Test signal processing (without actually starting the full system)
        # This would require mocking market data in a real test
        
        logger.info("✓ System integration tests passed")
    
    def test_performance_calculations(self):
        """Test performance metric calculations"""
        logger.info("Testing performance calculations...")

        # Reset paper trader for clean test
        fresh_trader = VolatilityPaperTrader(self.portfolio_size)

        # Get initial performance summary
        initial_summary = fresh_trader.get_performance_summary()

        assert initial_summary["initial_capital"] == self.portfolio_size
        assert initial_summary["total_trades"] == 0
        assert initial_summary["win_rate_pct"] == 0

        logger.info("✓ Performance calculation tests passed")

async def run_all_tests():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("THE ARCHITECT'S VOLATILITY SYSTEM TEST SUITE")
    logger.info("=" * 60)
    
    test_suite = TestVolatilitySystem()
    test_suite.setup_method()
    
    try:
        # Run all tests
        await test_suite.test_vix_data_collection()
        test_suite.test_options_greeks_calculation()
        test_suite.test_risk_management()
        await test_suite.test_paper_trading()
        test_suite.test_transaction_costs()
        test_suite.test_slippage_modeling()
        await test_suite.test_complete_system_integration()
        test_suite.test_performance_calculations()
        
        logger.info("=" * 60)
        logger.info("ALL TESTS PASSED ✓")
        logger.info("The Architect's system is ready for deployment")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        logger.error("=" * 60)
        logger.error("TESTS FAILED ✗")
        logger.error("Fix the issues before deployment")
        logger.error("=" * 60)
        raise

if __name__ == "__main__":
    asyncio.run(run_all_tests())
