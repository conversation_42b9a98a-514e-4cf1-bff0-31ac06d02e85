"""
Risk Management Framework for Volatility Trading
The Architect's implementation - this is what keeps you alive in the markets
"""
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import numpy as np

from models.options_greeks import OptionsGreeksCalculator

logger = logging.getLogger(__name__)

class VolatilityRiskManager:
    """
    The Architect's Risk Management System
    
    Core Principles:
    1. Never risk more than 1% per trade
    2. Never risk more than 5% per day
    3. Maximum 3 concurrent positions
    4. Delta-neutral maintenance
    5. Automatic kill switches
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size
        self.greeks_calculator = OptionsGreeksCalculator()
        
        # Risk limits
        self.max_position_risk = 0.01   # 1% per trade
        self.max_daily_risk = 0.05      # 5% per day
        self.max_concurrent_positions = 3
        self.max_portfolio_delta = 0.1  # 10% of portfolio
        self.max_portfolio_gamma = 0.02 # 2% per 1% Nifty move
        
        # Kill switches
        self.max_drawdown_limit = 0.25  # 25% max drawdown
        self.consecutive_loss_limit = 5
        
        # Current state
        self.current_positions: List[Dict] = []
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.consecutive_losses = 0
        self.max_drawdown = 0.0
        
    def validate_new_position(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate if a new position can be taken
        The Architect: This is your first line of defense
        """
        # Check position count limit
        if len(self.current_positions) >= self.max_concurrent_positions:
            return False, f"Maximum {self.max_concurrent_positions} positions already open"
        
        # Check daily loss limit
        if self.daily_pnl < -self.portfolio_size * self.max_daily_risk:
            return False, f"Daily loss limit exceeded: {self.daily_pnl:.0f}"
        
        # Check position size
        max_loss = signal.get("max_loss", 0)
        max_risk_amount = self.portfolio_size * self.max_position_risk
        
        if max_loss > max_risk_amount:
            return False, f"Position risk {max_loss:.0f} exceeds limit {max_risk_amount:.0f}"
        
        # Check consecutive losses
        if self.consecutive_losses >= self.consecutive_loss_limit:
            return False, f"Consecutive loss limit reached: {self.consecutive_losses}"
        
        # Check max drawdown
        if self.max_drawdown > self.max_drawdown_limit:
            return False, f"Max drawdown limit exceeded: {self.max_drawdown:.1%}"
        
        # Check portfolio Greeks after adding position
        portfolio_greeks = self.calculate_portfolio_greeks_with_new_position(signal)
        
        if abs(portfolio_greeks["total_delta"]) > self.max_portfolio_delta * self.portfolio_size:
            return False, f"Portfolio delta limit exceeded: {portfolio_greeks['total_delta']:.0f}"
        
        if abs(portfolio_greeks["total_gamma"]) > self.max_portfolio_gamma * self.portfolio_size:
            return False, f"Portfolio gamma limit exceeded: {portfolio_greeks['total_gamma']:.0f}"
        
        return True, "Position approved"
    
    def calculate_position_size(self, signal: Dict[str, Any]) -> int:
        """
        Calculate optimal position size based on risk parameters
        The Architect: Size positions based on risk, not greed
        """
        max_loss = signal.get("max_loss", 0)

        # Calculate maximum risk amount
        max_risk_amount = self.portfolio_size * self.max_position_risk

        # Adjust for current daily P&L
        remaining_daily_risk = (self.portfolio_size * self.max_daily_risk) + self.daily_pnl
        max_risk_amount = min(max_risk_amount, remaining_daily_risk)

        if max_risk_amount <= 0:
            return 0

        # If max_loss is provided, use it directly
        if max_loss > 0:
            lots = int(max_risk_amount / max_loss)
            return max(1, lots)  # Minimum 1 lot

        # Fallback: calculate based on premium
        total_premium = signal.get("total_premium", 0)
        if total_premium <= 0:
            # Default premium estimate for testing
            total_premium = 100.0  # ₹100 default

        premium_per_lot = total_premium * 75  # NSE lot size

        # For long vol: max loss = premium paid
        # For short vol: max loss = estimated based on Greeks
        if signal.get("strategy") == "LONG_VOLATILITY":
            max_loss_per_lot = premium_per_lot
        else:
            # Conservative estimate for short vol
            max_loss_per_lot = premium_per_lot * 3

        if max_loss_per_lot <= 0:
            return 1  # Default to 1 lot

        lots = int(max_risk_amount / max_loss_per_lot)

        # Ensure minimum viable position
        return max(1, lots)
    
    def calculate_portfolio_greeks(self) -> Dict[str, float]:
        """Calculate current portfolio Greeks"""
        if not self.current_positions:
            return {
                "total_delta": 0.0,
                "total_gamma": 0.0,
                "total_theta": 0.0,
                "total_vega": 0.0,
                "total_value": 0.0
            }
        
        return self.greeks_calculator.calculate_portfolio_greeks(self.current_positions)
    
    def calculate_portfolio_greeks_with_new_position(self, signal: Dict[str, Any]) -> Dict[str, float]:
        """Calculate portfolio Greeks including a potential new position"""
        # Convert signal to position format
        new_positions = []
        
        for option in signal.get("options", []):
            position = {
                "quantity": signal.get("lots", 1) * 75,  # NSE lot size
                "spot_price": 19500,  # Approximate Nifty level
                "strike_price": option.strike_price,
                "expiry_date": option.expiry_date,
                "volatility": option.implied_volatility / 100 if option.implied_volatility > 1 else 0.15,
                "option_type": option.option_type
            }
            
            # Adjust quantity for strategy type
            if signal.get("strategy") == "SHORT_VOLATILITY":
                position["quantity"] *= -1  # Short position
            
            new_positions.append(position)
        
        # Combine with current positions
        all_positions = self.current_positions + new_positions
        
        return self.greeks_calculator.calculate_portfolio_greeks(all_positions)
    
    def check_exit_conditions(self, position: Dict[str, Any], 
                             current_price: float) -> Tuple[bool, str]:
        """
        Check if a position should be exited
        The Architect: Know when to fold
        """
        entry_price = position.get("entry_price", 0)
        strategy = position.get("strategy", "")
        
        if entry_price <= 0:
            return False, "Invalid entry price"
        
        # Calculate current P&L
        if strategy == "LONG_VOLATILITY":
            pnl_pct = (current_price - entry_price) / entry_price
            
            # Profit target: 100% gain
            if pnl_pct >= 1.0:
                return True, f"Profit target hit: {pnl_pct:.1%} gain"
            
            # Stop loss: 50% loss
            if pnl_pct <= -0.5:
                return True, f"Stop loss hit: {pnl_pct:.1%} loss"
        
        else:  # SHORT_VOLATILITY
            pnl_pct = (entry_price - current_price) / entry_price
            
            # Profit target: 50% of premium
            if pnl_pct >= 0.5:
                return True, f"Profit target hit: {pnl_pct:.1%} gain"
            
            # Stop loss: 200% of premium
            if pnl_pct <= -2.0:
                return True, f"Stop loss hit: {pnl_pct:.1%} loss"
        
        # Time-based exit: 2 days before expiry
        expiry_date = position.get("expiry_date")
        if expiry_date:
            days_to_expiry = (expiry_date - datetime.now().date()).days
            if days_to_expiry <= 2:
                return True, f"Time exit: {days_to_expiry} days to expiry"
        
        return False, "No exit condition met"
    
    def update_daily_pnl(self, pnl_change: float):
        """Update daily P&L and check limits"""
        self.daily_pnl += pnl_change
        self.total_pnl += pnl_change
        
        # Update max drawdown
        if self.total_pnl < 0:
            drawdown = abs(self.total_pnl) / self.portfolio_size
            self.max_drawdown = max(self.max_drawdown, drawdown)
        
        # Update consecutive losses
        if pnl_change < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0
    
    def reset_daily_metrics(self):
        """Reset daily metrics at market open"""
        self.daily_pnl = 0.0
        logger.info("Daily risk metrics reset")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """Get current risk status"""
        portfolio_greeks = self.calculate_portfolio_greeks()
        
        return {
            "portfolio_size": self.portfolio_size,
            "daily_pnl": round(self.daily_pnl, 2),
            "total_pnl": round(self.total_pnl, 2),
            "max_drawdown": round(self.max_drawdown, 4),
            "consecutive_losses": self.consecutive_losses,
            "current_positions": len(self.current_positions),
            "max_positions": self.max_concurrent_positions,
            "portfolio_delta": round(portfolio_greeks["total_delta"], 2),
            "portfolio_gamma": round(portfolio_greeks["total_gamma"], 6),
            "portfolio_theta": round(portfolio_greeks["total_theta"], 2),
            "portfolio_vega": round(portfolio_greeks["total_vega"], 2),
            "risk_limits": {
                "max_position_risk": self.max_position_risk,
                "max_daily_risk": self.max_daily_risk,
                "max_drawdown_limit": self.max_drawdown_limit,
                "consecutive_loss_limit": self.consecutive_loss_limit
            },
            "kill_switches": {
                "daily_limit_breached": self.daily_pnl < -self.portfolio_size * self.max_daily_risk,
                "drawdown_limit_breached": self.max_drawdown > self.max_drawdown_limit,
                "consecutive_losses_breached": self.consecutive_losses >= self.consecutive_loss_limit
            },
            "timestamp": datetime.now().isoformat()
        }
    
    def add_position(self, position: Dict[str, Any]):
        """Add a new position to tracking"""
        self.current_positions.append(position)
        logger.info(f"Added position: {position.get('strategy', 'Unknown')} at {position.get('strike', 'Unknown')}")
    
    def remove_position(self, position_id: str):
        """Remove a position from tracking"""
        self.current_positions = [p for p in self.current_positions if p.get("id") != position_id]
        logger.info(f"Removed position: {position_id}")
    
    def should_halt_trading(self) -> Tuple[bool, str]:
        """Check if trading should be halted"""
        status = self.get_risk_status()
        
        for switch, breached in status["kill_switches"].items():
            if breached:
                return True, f"Kill switch activated: {switch}"
        
        return False, "Trading allowed"
