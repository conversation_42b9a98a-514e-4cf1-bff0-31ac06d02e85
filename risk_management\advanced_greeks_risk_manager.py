"""
Advanced Greeks Risk Management with Volatility Adjustment
The Architect's implementation - Greeks that adapt to market conditions
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class GreeksRiskLimits:
    """Dynamic Greeks risk limits based on market conditions"""
    max_portfolio_delta: float
    max_portfolio_gamma: float
    max_portfolio_vega: float
    max_portfolio_theta: float
    max_vega_per_expiry: float
    vix_adjustment_factor: float
    regime: str

@dataclass
class PortfolioGreeks:
    """Portfolio-level Greeks with risk analysis"""
    total_delta: float
    total_gamma: float
    total_vega: float
    total_theta: float
    delta_utilization: float
    gamma_utilization: float
    vega_utilization: float
    vega_concentration: Dict[str, float]  # By expiry
    risk_warnings: List[str]

class AdvancedGreeksRiskManager:
    """
    The Architect's Advanced Greeks Risk Management System
    
    Core Enhancements:
    - Volatility-adjusted gamma limits (50% reduction when VIX >20)
    - Vega concentration limits (max 30% per expiry)
    - Dynamic risk scaling based on market regime
    - Cross-expiry correlation adjustments
    - Real-time Greeks monitoring with alerts
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size
        
        # Base risk limits (adjusted dynamically)
        self.base_delta_limit = 0.1  # 10% of portfolio
        self.base_gamma_limit = 0.02  # 2% per 1% Nifty move
        self.base_vega_limit = 0.05   # 5% per 1% vol change
        self.base_theta_limit = 0.01  # 1% daily decay
        
        # Concentration limits
        self.max_vega_per_expiry = 0.3  # 30% max per expiry
        self.max_positions_per_expiry = 5
        
        # VIX adjustment thresholds
        self.vix_high_threshold = 20
        self.vix_extreme_threshold = 30
        
        # Current state
        self.current_positions: List[Dict] = []
        self.current_vix = 16.0
        self.current_regime = "NORMAL"
        
    def calculate_dynamic_limits(self, current_vix: float, regime: str) -> GreeksRiskLimits:
        """
        Calculate dynamic Greeks limits based on VIX and regime
        The Architect: Limits that adapt to market stress
        """
        self.current_vix = current_vix
        self.current_regime = regime
        
        # VIX adjustment factor
        if current_vix <= 15:
            vix_factor = 1.2  # Increase limits in low vol
        elif current_vix <= self.vix_high_threshold:
            vix_factor = 1.0  # Normal limits
        elif current_vix <= self.vix_extreme_threshold:
            vix_factor = 0.5  # 50% reduction as specified
        else:
            vix_factor = 0.25  # 75% reduction in extreme vol
        
        # Regime adjustments
        regime_adjustments = {
            "EXTREME_LOW_VOL": 1.5,   # Can take more risk in stable low vol
            "LOW_VOL": 1.2,
            "NORMAL_VOL": 1.0,
            "HIGH_VOL": 0.7,
            "EXTREME_HIGH_VOL": 0.3   # Minimal risk in crisis
        }
        
        regime_factor = regime_adjustments.get(regime, 1.0)
        
        # Combined adjustment
        total_adjustment = vix_factor * regime_factor
        
        return GreeksRiskLimits(
            max_portfolio_delta=self.base_delta_limit * total_adjustment * self.portfolio_size,
            max_portfolio_gamma=self.base_gamma_limit * total_adjustment * self.portfolio_size,
            max_portfolio_vega=self.base_vega_limit * total_adjustment * self.portfolio_size,
            max_portfolio_theta=self.base_theta_limit * total_adjustment * self.portfolio_size,
            max_vega_per_expiry=self.max_vega_per_expiry * total_adjustment,
            vix_adjustment_factor=total_adjustment,
            regime=regime
        )
    
    def calculate_portfolio_greeks(self, positions: List[Dict]) -> PortfolioGreeks:
        """
        Calculate comprehensive portfolio Greeks with risk analysis
        The Architect: Know your exposure at all times
        """
        if not positions:
            return self._empty_portfolio_greeks()
        
        # Aggregate Greeks
        total_delta = sum(pos.get('delta', 0) * pos.get('quantity', 0) for pos in positions)
        total_gamma = sum(pos.get('gamma', 0) * pos.get('quantity', 0) for pos in positions)
        total_vega = sum(pos.get('vega', 0) * pos.get('quantity', 0) for pos in positions)
        total_theta = sum(pos.get('theta', 0) * pos.get('quantity', 0) for pos in positions)
        
        # Calculate current limits
        current_limits = self.calculate_dynamic_limits(self.current_vix, self.current_regime)
        
        # Calculate utilization percentages
        delta_utilization = abs(total_delta) / current_limits.max_portfolio_delta if current_limits.max_portfolio_delta > 0 else 0
        gamma_utilization = abs(total_gamma) / current_limits.max_portfolio_gamma if current_limits.max_portfolio_gamma > 0 else 0
        vega_utilization = abs(total_vega) / current_limits.max_portfolio_vega if current_limits.max_portfolio_vega > 0 else 0
        
        # Calculate vega concentration by expiry
        vega_by_expiry = defaultdict(float)
        for pos in positions:
            expiry = pos.get('expiry_date', 'unknown')
            if isinstance(expiry, datetime):
                expiry_str = expiry.strftime('%Y-%m-%d')
            else:
                expiry_str = str(expiry)
            vega_by_expiry[expiry_str] += pos.get('vega', 0) * pos.get('quantity', 0)
        
        # Convert to concentration percentages
        total_portfolio_vega = abs(total_vega) if total_vega != 0 else 1
        vega_concentration = {
            expiry: abs(vega) / total_portfolio_vega 
            for expiry, vega in vega_by_expiry.items()
        }
        
        # Generate risk warnings
        risk_warnings = self._generate_risk_warnings(
            delta_utilization, gamma_utilization, vega_utilization, 
            vega_concentration, current_limits
        )
        
        return PortfolioGreeks(
            total_delta=total_delta,
            total_gamma=total_gamma,
            total_vega=total_vega,
            total_theta=total_theta,
            delta_utilization=delta_utilization,
            gamma_utilization=gamma_utilization,
            vega_utilization=vega_utilization,
            vega_concentration=vega_concentration,
            risk_warnings=risk_warnings
        )
    
    def validate_new_position_greeks(self, new_position: Dict, 
                                   existing_positions: List[Dict]) -> Tuple[bool, str]:
        """
        Validate if new position violates Greeks limits
        The Architect: Stop bad trades before they happen
        """
        # Calculate portfolio Greeks with new position
        all_positions = existing_positions + [new_position]
        portfolio_greeks = self.calculate_portfolio_greeks(all_positions)
        current_limits = self.calculate_dynamic_limits(self.current_vix, self.current_regime)
        
        # Check delta limit
        if abs(portfolio_greeks.total_delta) > current_limits.max_portfolio_delta:
            return False, f"Delta limit exceeded: {portfolio_greeks.total_delta:.0f} > {current_limits.max_portfolio_delta:.0f}"
        
        # Check gamma limit (critical in high vol)
        if abs(portfolio_greeks.total_gamma) > current_limits.max_portfolio_gamma:
            return False, f"Gamma limit exceeded: {portfolio_greeks.total_gamma:.4f} > {current_limits.max_portfolio_gamma:.4f}"
        
        # Check vega limit
        if abs(portfolio_greeks.total_vega) > current_limits.max_portfolio_vega:
            return False, f"Vega limit exceeded: {portfolio_greeks.total_vega:.0f} > {current_limits.max_portfolio_vega:.0f}"
        
        # Check vega concentration per expiry
        new_expiry = new_position.get('expiry_date', 'unknown')
        if isinstance(new_expiry, datetime):
            new_expiry_str = new_expiry.strftime('%Y-%m-%d')
        else:
            new_expiry_str = str(new_expiry)
        
        expiry_concentration = portfolio_greeks.vega_concentration.get(new_expiry_str, 0)
        if expiry_concentration > current_limits.max_vega_per_expiry:
            return False, f"Vega concentration limit exceeded for {new_expiry_str}: {expiry_concentration:.1%} > {current_limits.max_vega_per_expiry:.1%}"
        
        # Check theta (for short vol positions)
        if portfolio_greeks.total_theta < -current_limits.max_portfolio_theta:
            return False, f"Theta limit exceeded: {portfolio_greeks.total_theta:.0f} < -{current_limits.max_portfolio_theta:.0f}"
        
        return True, "Greeks validation passed"
    
    def get_position_sizing_adjustment(self, base_lots: int, new_position: Dict) -> int:
        """
        Adjust position size based on Greeks constraints
        The Architect: Size positions to fit within risk envelope
        """
        if base_lots <= 0:
            return 0
        
        # Test different lot sizes to find maximum allowable
        for lots in range(base_lots, 0, -1):
            test_position = new_position.copy()
            test_position['quantity'] = lots * 75  # NSE lot size
            
            # Recalculate Greeks for test position
            test_position = self._update_position_greeks(test_position)
            
            is_valid, _ = self.validate_new_position_greeks(test_position, self.current_positions)
            if is_valid:
                return lots
        
        return 0  # No valid size found
    
    def _update_position_greeks(self, position: Dict) -> Dict:
        """Update position with calculated Greeks"""
        # Simplified Greeks calculation (in production, use proper options pricing)
        quantity = position.get('quantity', 0)
        spot_price = position.get('spot_price', 19500)
        strike = position.get('strike_price', spot_price)
        
        # Rough Greeks estimates (replace with real calculation)
        moneyness = spot_price / strike
        
        if position.get('option_type') == 'CE':
            delta = max(0, min(1, moneyness - 0.5)) * quantity
            gamma = 0.01 * quantity if 0.9 < moneyness < 1.1 else 0.005 * quantity
        else:  # PE
            delta = -max(0, min(1, 1.5 - moneyness)) * quantity
            gamma = 0.01 * quantity if 0.9 < moneyness < 1.1 else 0.005 * quantity
        
        vega = 0.1 * quantity  # Simplified
        theta = -0.05 * quantity  # Time decay
        
        position.update({
            'delta': delta,
            'gamma': gamma,
            'vega': vega,
            'theta': theta
        })
        
        return position
    
    def _generate_risk_warnings(self, delta_util: float, gamma_util: float, 
                              vega_util: float, vega_conc: Dict[str, float],
                              limits: GreeksRiskLimits) -> List[str]:
        """Generate risk warnings based on utilization"""
        warnings = []
        
        # High utilization warnings
        if delta_util > 0.8:
            warnings.append(f"High delta utilization: {delta_util:.1%}")
        
        if gamma_util > 0.8:
            warnings.append(f"High gamma utilization: {gamma_util:.1%} (VIX={self.current_vix:.1f})")
        
        if vega_util > 0.8:
            warnings.append(f"High vega utilization: {vega_util:.1%}")
        
        # Concentration warnings
        for expiry, concentration in vega_conc.items():
            if concentration > 0.25:  # 25% warning threshold
                warnings.append(f"High vega concentration in {expiry}: {concentration:.1%}")
        
        # Regime warnings
        if self.current_vix > self.vix_extreme_threshold:
            warnings.append(f"Extreme volatility regime: VIX={self.current_vix:.1f}")
        
        return warnings
    
    def _empty_portfolio_greeks(self) -> PortfolioGreeks:
        """Return empty portfolio Greeks"""
        return PortfolioGreeks(
            total_delta=0.0,
            total_gamma=0.0,
            total_vega=0.0,
            total_theta=0.0,
            delta_utilization=0.0,
            gamma_utilization=0.0,
            vega_utilization=0.0,
            vega_concentration={},
            risk_warnings=[]
        )
    
    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive Greeks risk dashboard"""
        portfolio_greeks = self.calculate_portfolio_greeks(self.current_positions)
        current_limits = self.calculate_dynamic_limits(self.current_vix, self.current_regime)
        
        return {
            "portfolio_greeks": {
                "delta": portfolio_greeks.total_delta,
                "gamma": portfolio_greeks.total_gamma,
                "vega": portfolio_greeks.total_vega,
                "theta": portfolio_greeks.total_theta
            },
            "utilization": {
                "delta": f"{portfolio_greeks.delta_utilization:.1%}",
                "gamma": f"{portfolio_greeks.gamma_utilization:.1%}",
                "vega": f"{portfolio_greeks.vega_utilization:.1%}"
            },
            "limits": {
                "delta": current_limits.max_portfolio_delta,
                "gamma": current_limits.max_portfolio_gamma,
                "vega": current_limits.max_portfolio_vega,
                "theta": current_limits.max_portfolio_theta
            },
            "vega_concentration": portfolio_greeks.vega_concentration,
            "risk_warnings": portfolio_greeks.risk_warnings,
            "market_conditions": {
                "vix": self.current_vix,
                "regime": self.current_regime,
                "adjustment_factor": current_limits.vix_adjustment_factor
            }
        }
    
    def update_positions(self, positions: List[Dict]):
        """Update current positions for Greeks tracking"""
        self.current_positions = positions
