"""
The Architect's Advanced Fixes Validation Test
Testing all 7 critical improvements to the volatility trading system
"""
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import all the new systems
from utils.dynamic_vix_percentiles import DynamicVIXPercentiles
from utils.liquidity_metrics import RealLiquidityMetrics
from risk_management.advanced_greeks_risk_manager import AdvancedGreeksRiskManager
from execution.realistic_nse_execution import RealisticNSEExecution
from analysis.regime_transition_hmm import VolatilityRegimeHMM
from risk_management.dynamic_position_sizing import DynamicPositionSizing
from analysis.global_correlation_monitor import GlobalCorrelationMonitor
from models.data_models import OptionsData

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ArchitectAdvancedFixesValidator:
    """
    The Architect's Comprehensive Validation Suite
    Testing every critical fix to ensure the system actually works
    """
    
    def __init__(self):
        self.test_results = {}
        self.portfolio_size = 1000000  # ₹10 lakh
        
    async def run_all_tests(self):
        """Run all validation tests"""
        logger.info("🚀 THE ARCHITECT'S ADVANCED FIXES VALIDATION")
        logger.info("=" * 60)
        
        tests = [
            ("Dynamic VIX Percentiles", self.test_dynamic_vix_percentiles),
            ("Real Liquidity Metrics", self.test_liquidity_metrics),
            ("Advanced Greeks Risk Management", self.test_advanced_greeks),
            ("Realistic NSE Execution", self.test_realistic_execution),
            ("Regime Transition HMM", self.test_regime_hmm),
            ("Dynamic Position Sizing", self.test_dynamic_position_sizing),
            ("Global Correlation Monitor", self.test_global_correlation_monitor)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n🔍 Testing: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result["passed"]:
                    logger.info(f"✅ PASS: {test_name}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ FAIL: {test_name} - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"💥 ERROR: {test_name} - {str(e)}")
                self.test_results[test_name] = {"passed": False, "error": str(e)}
        
        # Final summary
        logger.info("\n" + "=" * 60)
        logger.info("🎯 THE ARCHITECT'S VALIDATION RESULTS")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            logger.info(f"{status} {test_name}")
            if not result["passed"]:
                logger.info(f"    Error: {result.get('error', 'Unknown')}")
        
        logger.info(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED - The Architect's fixes are working!")
        else:
            logger.warning("⚠️  Some tests failed - system needs attention")
        
        return passed_tests == total_tests
    
    async def test_dynamic_vix_percentiles(self):
        """Test 1: Dynamic VIX Percentiles System"""
        try:
            vix_percentiles = DynamicVIXPercentiles()
            
            # Create sample VIX data
            dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
            vix_values = 15 + 5 * np.sin(np.arange(len(dates)) * 0.1) + np.random.normal(0, 2, len(dates))
            vix_data = pd.DataFrame({'vix': vix_values}, index=dates)
            
            # Test percentile calculation
            percentile_data = vix_percentiles.calculate_dynamic_percentiles(vix_data)
            
            # Validate results
            assert percentile_data.current_vix > 0, "Current VIX should be positive"
            assert 0 <= percentile_data.percentile_rank <= 100, "Percentile rank should be 0-100"
            assert percentile_data.p20_threshold < percentile_data.p80_threshold, "P20 should be less than P80"
            assert percentile_data.confidence > 0, "Confidence should be positive"
            
            # Test trading signals
            signals = vix_percentiles.get_trading_signals(percentile_data)
            assert "long_volatility" in signals, "Should have long volatility signal"
            assert "short_volatility" in signals, "Should have short volatility signal"
            assert "confidence" in signals, "Should have confidence score"
            
            # Test recalibration check
            needs_recalibration = vix_percentiles.should_recalibrate()
            assert isinstance(needs_recalibration, bool), "Recalibration check should return boolean"
            
            return {
                "passed": True,
                "details": {
                    "current_vix": percentile_data.current_vix,
                    "percentile_rank": percentile_data.percentile_rank,
                    "regime": percentile_data.regime,
                    "confidence": percentile_data.confidence,
                    "p20_threshold": percentile_data.p20_threshold,
                    "p80_threshold": percentile_data.p80_threshold
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_liquidity_metrics(self):
        """Test 2: Real Liquidity Metrics Framework"""
        try:
            liquidity_metrics = RealLiquidityMetrics()
            
            # Create sample options data
            sample_option = OptionsData(
                symbol="NIFTY",
                strike=19500,
                expiry_date=datetime.now() + timedelta(days=10),
                option_type="CE",
                last_price=100,
                volume=1500,
                open_interest=2000,
                bid_price=98,
                ask_price=102,
                bid_qty=100,
                ask_qty=150,
                implied_volatility=18.5,
                timestamp=datetime.now()
            )
            
            # Test liquidity calculation
            metrics = liquidity_metrics.calculate_liquidity_metrics(sample_option)
            
            # Validate results
            assert metrics.effective_spread > 0, "Effective spread should be positive"
            assert metrics.effective_spread_pct > 0, "Effective spread % should be positive"
            assert metrics.total_market_depth > 0, "Market depth should be positive"
            assert 0 <= metrics.liquidity_score <= 100, "Liquidity score should be 0-100"
            assert isinstance(metrics.tradeable, bool), "Tradeable should be boolean"
            
            # Test execution cost estimation
            cost_estimate = liquidity_metrics.get_execution_cost_estimate(metrics, 2)
            assert "total_cost" in cost_estimate, "Should have total cost"
            assert "spread_cost" in cost_estimate, "Should have spread cost"
            assert "impact_cost" in cost_estimate, "Should have impact cost"
            assert cost_estimate["total_cost"] > 0, "Total cost should be positive"
            
            # Test filtering
            options_list = [sample_option]
            tradeable_options = liquidity_metrics.filter_tradeable_options(options_list)
            assert isinstance(tradeable_options, list), "Should return list"
            
            return {
                "passed": True,
                "details": {
                    "effective_spread_pct": metrics.effective_spread_pct,
                    "market_depth": metrics.total_market_depth,
                    "liquidity_score": metrics.liquidity_score,
                    "tradeable": metrics.tradeable,
                    "execution_cost": cost_estimate["total_cost"]
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_advanced_greeks(self):
        """Test 3: Advanced Greeks Risk Management"""
        try:
            greeks_manager = AdvancedGreeksRiskManager(self.portfolio_size)
            
            # Test dynamic limits calculation
            limits = greeks_manager.calculate_dynamic_limits(current_vix=22, regime="HIGH_VOL")
            
            assert limits.max_portfolio_delta > 0, "Delta limit should be positive"
            assert limits.max_portfolio_gamma > 0, "Gamma limit should be positive"
            assert limits.max_portfolio_vega > 0, "Vega limit should be positive"
            assert limits.vix_adjustment_factor <= 1.0, "Should reduce limits for high VIX"
            
            # Test portfolio Greeks calculation
            sample_positions = [
                {
                    'delta': 1000, 'gamma': 0.5, 'vega': 200, 'theta': -50,
                    'quantity': 75, 'expiry_date': datetime.now() + timedelta(days=10)
                }
            ]
            
            portfolio_greeks = greeks_manager.calculate_portfolio_greeks(sample_positions)
            
            assert hasattr(portfolio_greeks, 'total_delta'), "Should have total delta"
            assert hasattr(portfolio_greeks, 'delta_utilization'), "Should have utilization"
            assert isinstance(portfolio_greeks.risk_warnings, list), "Should have risk warnings list"
            
            # Test position validation
            new_position = {
                'delta': 500, 'gamma': 0.2, 'vega': 100, 'theta': -25,
                'quantity': 75, 'expiry_date': datetime.now() + timedelta(days=15)
            }
            
            is_valid, message = greeks_manager.validate_new_position_greeks(new_position, sample_positions)
            assert isinstance(is_valid, bool), "Validation should return boolean"
            assert isinstance(message, str), "Should return validation message"
            
            return {
                "passed": True,
                "details": {
                    "vix_adjustment_factor": limits.vix_adjustment_factor,
                    "delta_limit": limits.max_portfolio_delta,
                    "gamma_limit": limits.max_portfolio_gamma,
                    "portfolio_delta": portfolio_greeks.total_delta,
                    "delta_utilization": portfolio_greeks.delta_utilization,
                    "position_valid": is_valid
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_realistic_execution(self):
        """Test 4: Realistic NSE Execution Simulation"""
        try:
            execution_engine = RealisticNSEExecution()
            
            # Test order execution
            sample_order = {
                'order_id': 'TEST001',
                'symbol': 'NIFTY',
                'strike': 19500,
                'option_type': 'CE',
                'quantity': 150,  # 2 lots
                'order_type': 'MARKET'
            }
            
            sample_market_data = {
                'bid_price': 98,
                'ask_price': 102,
                'last_price': 100,
                'volume': 1000,
                'open_interest': 5000
            }
            
            # Execute order
            result = execution_engine.execute_order(sample_order, sample_market_data)
            
            # Validate execution result
            assert hasattr(result, 'status'), "Should have execution status"
            assert hasattr(result, 'filled_quantity'), "Should have filled quantity"
            assert hasattr(result, 'avg_fill_price'), "Should have fill price"
            assert hasattr(result, 'execution_time_ms'), "Should have execution time"
            assert result.execution_time_ms > 0, "Execution time should be positive"
            
            # Test with different VIX levels
            execution_engine.update_market_conditions(vix=30, stress_factor=2.0)
            
            result_high_vix = execution_engine.execute_order(sample_order, sample_market_data)
            
            # High VIX should affect execution
            assert hasattr(result_high_vix, 'status'), "Should handle high VIX execution"
            
            return {
                "passed": True,
                "details": {
                    "execution_status": result.status.value,
                    "filled_quantity": result.filled_quantity,
                    "execution_time_ms": result.execution_time_ms,
                    "slippage": result.slippage,
                    "impact_cost": result.impact_cost,
                    "high_vix_status": result_high_vix.status.value
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_regime_hmm(self):
        """Test 5: Regime Transition HMM"""
        try:
            regime_hmm = VolatilityRegimeHMM()
            
            # Create sample VIX data for HMM
            dates = pd.date_range(start='2022-01-01', end='2024-01-01', freq='D')
            
            # Create regime-like VIX data
            vix_values = []
            for i, date in enumerate(dates):
                if i < len(dates) // 3:
                    vix_values.append(12 + np.random.normal(0, 2))  # Low vol regime
                elif i < 2 * len(dates) // 3:
                    vix_values.append(18 + np.random.normal(0, 3))  # Normal vol regime
                else:
                    vix_values.append(28 + np.random.normal(0, 5))  # High vol regime
            
            vix_data = pd.DataFrame({'vix': vix_values}, index=dates)
            
            # Test HMM fitting
            fit_success = regime_hmm.fit_regime_model(vix_data)
            assert fit_success, "HMM should fit successfully"
            assert regime_hmm.is_fitted, "HMM should be marked as fitted"
            
            # Test regime prediction
            recent_data = vix_data.tail(30)
            regime_transition = regime_hmm.predict_current_regime(recent_data)
            
            assert hasattr(regime_transition, 'current_regime'), "Should have current regime"
            assert hasattr(regime_transition, 'uncertainty'), "Should have uncertainty"
            assert hasattr(regime_transition, 'confidence'), "Should have confidence"
            assert 0 <= regime_transition.uncertainty <= 1, "Uncertainty should be 0-1"
            assert 0 <= regime_transition.confidence <= 1, "Confidence should be 0-1"
            
            # Test trading filters
            trading_filters = regime_hmm.get_trading_filters(regime_transition)
            assert "allow_trading" in trading_filters, "Should have trading permission"
            assert "position_size_multiplier" in trading_filters, "Should have size multiplier"
            
            return {
                "passed": True,
                "details": {
                    "fitted_successfully": fit_success,
                    "current_regime": regime_transition.current_regime,
                    "regime_name": regime_transition.current_regime_name,
                    "uncertainty": regime_transition.uncertainty,
                    "confidence": regime_transition.confidence,
                    "allow_trading": trading_filters["allow_trading"]
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_dynamic_position_sizing(self):
        """Test 6: Dynamic Position Sizing with Kelly Criterion"""
        try:
            position_sizer = DynamicPositionSizing(self.portfolio_size)
            
            # Add some sample trade history
            sample_trades = [
                {'pnl': 1000}, {'pnl': -500}, {'pnl': 1500}, {'pnl': -300},
                {'pnl': 800}, {'pnl': -400}, {'pnl': 1200}, {'pnl': -600},
                {'pnl': 900}, {'pnl': -200}, {'pnl': 1100}, {'pnl': -700}
            ]
            
            for trade in sample_trades:
                position_sizer.add_trade_result(trade)
            
            # Test Kelly calculation
            kelly_params = position_sizer.calculate_kelly_fraction(sample_trades)
            
            assert kelly_params.win_rate > 0, "Win rate should be positive"
            assert kelly_params.kelly_fraction >= 0, "Kelly fraction should be non-negative"
            assert kelly_params.sample_size == len(sample_trades), "Sample size should match"
            
            # Test position sizing
            sample_signal = {
                'max_loss': 5000,
                'total_premium': 100,
                'strategy': 'LONG_VOLATILITY'
            }
            
            current_positions = []
            
            sizing_result = position_sizer.calculate_position_size(
                signal=sample_signal,
                current_positions=current_positions,
                current_vix=20
            )
            
            assert hasattr(sizing_result, 'final_lots'), "Should have final lot size"
            assert hasattr(sizing_result, 'size_factors'), "Should have size factors"
            assert sizing_result.final_lots >= 0, "Final lots should be non-negative"
            
            # Test with high VIX (should reduce size)
            sizing_result_high_vix = position_sizer.calculate_position_size(
                signal=sample_signal,
                current_positions=current_positions,
                current_vix=30
            )
            
            assert sizing_result_high_vix.final_lots <= sizing_result.final_lots, "High VIX should reduce position size"
            
            return {
                "passed": True,
                "details": {
                    "kelly_fraction": kelly_params.kelly_fraction,
                    "win_rate": kelly_params.win_rate,
                    "confidence": kelly_params.confidence,
                    "normal_vix_lots": sizing_result.final_lots,
                    "high_vix_lots": sizing_result_high_vix.final_lots,
                    "volatility_factor": sizing_result_high_vix.size_factors["volatility_factor"]
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    async def test_global_correlation_monitor(self):
        """Test 7: Global Correlation Monitoring System"""
        try:
            correlation_monitor = GlobalCorrelationMonitor()
            
            # Test with synthetic data generation instead of manual data
            # This will use the correlation monitor's own data generation
            success = await correlation_monitor.update_market_data()
            assert success, "Should successfully generate market data"

            
            # Test correlation calculation
            correlation_matrix = correlation_monitor.calculate_correlation_matrix()
            
            assert correlation_matrix is not None, "Should calculate correlation matrix"
            assert correlation_matrix.shape[0] == correlation_matrix.shape[1], "Should be square matrix"
            
            # Test high correlation detection
            alerts = correlation_monitor.detect_high_correlations()
            assert isinstance(alerts, list), "Should return list of alerts"
            
            # Test position limits
            position_limits = correlation_monitor.get_position_limits()
            assert "max_positions" in position_limits, "Should have max positions"
            assert "max_correlation" in position_limits, "Should have max correlation"
            assert position_limits["max_positions"] > 0, "Max positions should be positive"
            
            # Test correlation dashboard
            dashboard = correlation_monitor.get_correlation_dashboard()
            assert "correlation_matrix" in dashboard, "Should have correlation matrix"
            assert "position_limits" in dashboard, "Should have position limits"
            
            return {
                "passed": True,
                "details": {
                    "correlation_matrix_size": correlation_matrix.shape,
                    "max_correlation": position_limits["max_correlation"],
                    "max_positions": position_limits["max_positions"],
                    "correlation_regime": position_limits["correlation_regime"],
                    "alerts_count": len(alerts)
                }
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}

async def main():
    """Run the comprehensive validation"""
    validator = ArchitectAdvancedFixesValidator()
    success = await validator.run_all_tests()
    
    if success:
        print("\n🎉 THE ARCHITECT'S ADVANCED FIXES - ALL SYSTEMS OPERATIONAL")
        print("The volatility trading system is now ready for real market conditions!")
    else:
        print("\n⚠️  Some systems need attention before deployment")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
