"""
De<PERSON>t for The Architect's Enhanced Volatility Trading System
Shows all the critical upgrades in action: real data, surface analysis, tail hedging, regime detection
"""
import asyncio
import logging
from datetime import datetime

from main_volatility_system import VolatilityTradingSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_enhanced_system():
    """
    Run a comprehensive demo of The Architect's enhanced volatility trading system
    """
    logger.info("=" * 80)
    logger.info("THE ARCHITECT'S ENHANCED VOLATILITY TRADING SYSTEM - LIVE DEMO")
    logger.info("=" * 80)
    logger.info("Upgrades: Real VIX Data | Surface Analysis | Tail Hedging | Regime Detection | Kill Switches")
    logger.info("=" * 80)
    
    # Initialize enhanced system with 10 lakh portfolio
    system = VolatilityTradingSystem(portfolio_size=1000000)
    
    try:
        logger.info("Initializing enhanced system components...")
        
        # Initialize historical VIX data
        logger.info("Loading historical VIX data (2008-present) for regime analysis...")
        async with system.real_vix_collector as collector:
            system.historical_vix_data = await collector.get_real_india_vix_data("2008-01-01")
            
            # Analyze crisis periods
            crisis_analysis = collector.analyze_crisis_periods(system.historical_vix_data)
            
            logger.info(f"Historical VIX Analysis:")
            logger.info(f"  - Total records: {len(system.historical_vix_data)}")
            logger.info(f"  - VIX range: {system.historical_vix_data['vix'].min():.1f} - {system.historical_vix_data['vix'].max():.1f}")
            logger.info(f"  - Days above 40: {crisis_analysis['overall']['days_above_40']}")
            logger.info(f"  - 95th percentile: {crisis_analysis['overall']['95th_percentile']:.1f}")
            
            # Get current VIX percentile filters
            vix_filters = collector.get_vix_percentile_filters(system.historical_vix_data)
            logger.info(f"  - Current VIX percentile: {vix_filters.get('current_percentile', 0):.1%}")
            logger.info(f"  - Long vol signal: {vix_filters.get('trade_long_vol', False)}")
            logger.info(f"  - Short vol signal: {vix_filters.get('trade_short_vol', False)}")
        
        # Perform regime analysis
        logger.info("\nPerforming volatility regime analysis...")
        regime_analysis = system.regime_detector.detect_current_regime(system.historical_vix_data)
        
        logger.info(f"Regime Analysis:")
        logger.info(f"  - Current regime: {regime_analysis['current_regime']}")
        logger.info(f"  - Risk level: {regime_analysis['risk_level']}")
        logger.info(f"  - Regime confidence: {regime_analysis['regime_confidence']:.1%}")
        logger.info(f"  - Transition probability: {regime_analysis['transition_probability']:.1%}")
        logger.info(f"  - Trading recommendation: {regime_analysis['trading_recommendation']}")
        
        # Get regime-based trading filters
        regime_filters = system.regime_detector.get_regime_trading_filters(regime_analysis)
        logger.info(f"  - New positions allowed: {regime_filters['allow_new_positions']}")
        logger.info(f"  - Position size multiplier: {regime_filters['max_position_size_multiplier']:.1f}")
        
        # Run enhanced trading cycles
        logger.info("\nStarting enhanced trading cycles...")
        
        system.is_running = True
        system.system_start_time = datetime.now()
        system.risk_manager.reset_daily_metrics()
        
        # Run 3 enhanced trading cycles
        for cycle in range(3):
            logger.info(f"\n--- Enhanced Trading Cycle {cycle + 1} ---")
            
            try:
                await system._trading_cycle()
                
                # Show enhanced status
                performance = system.paper_trader.get_performance_summary()
                failure_stats = system.catastrophic_monitor.get_failure_statistics()
                
                logger.info(f"Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
                logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f}")
                logger.info(f"Active Positions: {performance['active_positions']}")
                logger.info(f"Failure Events: {failure_stats['total_failure_events']}")
                logger.info(f"System Status: {failure_stats['system_status']}")
                
                # Wait between cycles
                await asyncio.sleep(3)
                
            except Exception as e:
                logger.error(f"Error in enhanced cycle {cycle + 1}: {e}")
                continue
        
        # Demonstrate catastrophic failure monitoring
        logger.info("\n--- Catastrophic Failure Monitoring Demo ---")
        
        current_portfolio_value = system.paper_trader.get_portfolio_value()
        current_vix = await system._get_current_vix()
        active_positions = list(system.paper_trader.active_positions.values())
        
        # Test normal conditions
        normal_check = system.catastrophic_monitor.check_catastrophic_conditions(
            current_portfolio_value, current_vix, active_positions
        )
        
        logger.info(f"Normal conditions check:")
        logger.info(f"  - Catastrophic failure: {normal_check['catastrophic_failure_detected']}")
        logger.info(f"  - Severity: {normal_check['severity']}")
        logger.info(f"  - Warnings: {len(normal_check.get('recommendations', []))}")
        
        # Test simulated VIX spike scenario
        logger.info(f"\nSimulating VIX spike scenario (VIX = 45)...")
        
        # Create mock short vol position for testing
        mock_short_vol = [{"strategy": "SHORT_VOLATILITY", "lots": 2}]
        
        spike_check = system.catastrophic_monitor.check_catastrophic_conditions(
            current_portfolio_value, 45.0, mock_short_vol
        )
        
        logger.info(f"VIX spike scenario:")
        logger.info(f"  - Catastrophic failure: {spike_check['catastrophic_failure_detected']}")
        logger.info(f"  - Failure type: {spike_check.get('failure_type', 'None')}")
        logger.info(f"  - Liquidation required: {spike_check.get('liquidation_required', False)}")
        
        # Demonstrate volatility surface analysis
        logger.info("\n--- Volatility Surface Analysis Demo ---")
        
        # Get real options data
        options_data = await system.strategy.get_nifty_options_data()
        
        if options_data:
            spot_price = system.strategy._estimate_spot_price(options_data)
            
            logger.info(f"Analyzing volatility surface for {len(options_data)} options...")
            
            surface_analysis = system.surface_analyzer.analyze_volatility_surface(
                options_data, spot_price
            )
            
            surface_metrics = surface_analysis.get("surface_metrics", {})
            term_structure = surface_analysis.get("term_structure", {})
            
            logger.info(f"Surface Analysis:")
            logger.info(f"  - Average IV: {surface_metrics.get('average_iv', 0):.1%}")
            logger.info(f"  - IV Range: {surface_metrics.get('iv_range', 0):.1%}")
            logger.info(f"  - Term structure: {term_structure.get('contango_backwardation', 'Unknown')}")
            logger.info(f"  - Expiries analyzed: {len(surface_analysis.get('expiry_analysis', {}))}")
            
            # Get surface-based trading signals
            surface_signals = system.surface_analyzer.get_trading_signals_from_surface(
                surface_analysis, vix_filters
            )
            
            logger.info(f"  - Surface recommendation: {surface_signals['trade_recommendation']}")
            logger.info(f"  - Surface confidence: {surface_signals['confidence']:.1%}")
            logger.info(f"  - Reasoning: {surface_signals['reasoning']}")
        
        # Generate final enhanced report
        await system._generate_final_report()
        
        logger.info("\n" + "=" * 80)
        logger.info("ENHANCED SYSTEM DEMO COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        logger.info("Key Enhancements Demonstrated:")
        logger.info("✓ Real historical VIX data with crisis period analysis")
        logger.info("✓ Volatility regime detection and transition monitoring")
        logger.info("✓ Volatility surface analysis with term structure")
        logger.info("✓ Catastrophic failure monitoring with kill switches")
        logger.info("✓ Enhanced risk management with percentile filters")
        logger.info("✓ Real NSE options data integration")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"Enhanced demo error: {e}")
        logger.error("=" * 80)
        logger.error("ENHANCED DEMO FAILED")
        logger.error("=" * 80)
    finally:
        # Clean shutdown
        system.is_running = False
        
        # Close any open positions
        for position_id in list(system.paper_trader.active_positions.keys()):
            await system.paper_trader.close_position(position_id, "Demo end")
        
        logger.info("Enhanced demo shutdown completed")

if __name__ == "__main__":
    asyncio.run(demo_enhanced_system())
