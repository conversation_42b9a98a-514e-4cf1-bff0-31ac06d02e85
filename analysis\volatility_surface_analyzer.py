"""
Volatility Surface Analyzer
The Architect's implementation - understand what you're actually paying for volatility
"""
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from scipy.interpolate import griddata
from scipy.stats import norm

from models.options_greeks import OptionsGreeksCalculator
from models.data_models import OptionsData

logger = logging.getLogger(__name__)

class VolatilitySurfaceAnalyzer:
    """
    Volatility surface analysis and term structure monitoring
    The Architect: This tells you if you're buying expensive or cheap volatility
    """
    
    def __init__(self):
        self.greeks_calculator = OptionsGreeksCalculator()
        
    def analyze_volatility_surface(self, options_data: List[OptionsData], 
                                 spot_price: float) -> Dict[str, Any]:
        """
        Analyze the complete volatility surface
        The Architect: This is where you see if the market is pricing vol correctly
        """
        if not options_data:
            return {"error": "No options data provided"}
        
        # Group options by expiry
        expiry_groups = {}
        for option in options_data:
            expiry_key = option.expiry_date
            if expiry_key not in expiry_groups:
                expiry_groups[expiry_key] = {"CE": [], "PE": []}
            expiry_groups[expiry_key][option.option_type].append(option)
        
        surface_analysis = {
            "spot_price": spot_price,
            "analysis_time": datetime.now().isoformat(),
            "expiry_analysis": {},
            "term_structure": {},
            "skew_analysis": {},
            "surface_metrics": {}
        }
        
        # Analyze each expiry
        for expiry_date, options_by_type in expiry_groups.items():
            if len(options_by_type["CE"]) > 3 and len(options_by_type["PE"]) > 3:
                expiry_analysis = self._analyze_expiry_slice(
                    options_by_type, spot_price, expiry_date
                )
                surface_analysis["expiry_analysis"][expiry_date.isoformat()] = expiry_analysis
        
        # Build term structure
        surface_analysis["term_structure"] = self._analyze_term_structure(
            surface_analysis["expiry_analysis"], spot_price
        )
        
        # Analyze skew
        surface_analysis["skew_analysis"] = self._analyze_volatility_skew_summary(
            surface_analysis["expiry_analysis"]
        )
        
        # Calculate surface-wide metrics
        surface_analysis["surface_metrics"] = self._calculate_surface_metrics(
            surface_analysis["expiry_analysis"]
        )
        
        return surface_analysis
    
    def _analyze_expiry_slice(self, options_by_type: Dict, spot_price: float, 
                            expiry_date: date) -> Dict[str, Any]:
        """Analyze volatility for a single expiry"""
        calls = options_by_type["CE"]
        puts = options_by_type["PE"]
        
        # Calculate time to expiry
        if isinstance(expiry_date, datetime):
            expiry_date = expiry_date.date()
        dte = (expiry_date - datetime.now().date()).days
        time_to_expiry = dte / 365.0
        
        slice_analysis = {
            "expiry_date": expiry_date.isoformat(),
            "days_to_expiry": dte,
            "time_to_expiry": time_to_expiry,
            "call_analysis": {},
            "put_analysis": {},
            "atm_iv": None,
            "iv_skew": {},
            "put_call_parity": {}
        }
        
        # Analyze calls
        call_strikes = []
        call_ivs = []
        for call in calls:
            if call.implied_volatility and call.implied_volatility > 0:
                iv = call.implied_volatility / 100 if call.implied_volatility > 1 else call.implied_volatility
                call_strikes.append(call.strike)
                call_ivs.append(iv)
        
        # Analyze puts
        put_strikes = []
        put_ivs = []
        for put in puts:
            if put.implied_volatility and put.implied_volatility > 0:
                iv = put.implied_volatility / 100 if put.implied_volatility > 1 else put.implied_volatility
                put_strikes.append(put.strike)
                put_ivs.append(iv)
        
        if call_strikes and put_strikes:
            # Find ATM IV
            atm_call_iv = self._find_atm_iv(call_strikes, call_ivs, spot_price)
            atm_put_iv = self._find_atm_iv(put_strikes, put_ivs, spot_price)
            slice_analysis["atm_iv"] = (atm_call_iv + atm_put_iv) / 2 if atm_call_iv and atm_put_iv else None
            
            # Calculate skew metrics
            slice_analysis["iv_skew"] = self._calculate_skew_metrics(
                call_strikes, call_ivs, put_strikes, put_ivs, spot_price
            )
            
            # Store strike-IV data
            slice_analysis["call_analysis"] = {
                "strikes": call_strikes,
                "implied_vols": call_ivs,
                "min_iv": min(call_ivs) if call_ivs else None,
                "max_iv": max(call_ivs) if call_ivs else None
            }
            
            slice_analysis["put_analysis"] = {
                "strikes": put_strikes,
                "implied_vols": put_ivs,
                "min_iv": min(put_ivs) if put_ivs else None,
                "max_iv": max(put_ivs) if put_ivs else None
            }
        
        return slice_analysis
    
    def _find_atm_iv(self, strikes: List[float], ivs: List[float], 
                    spot_price: float) -> Optional[float]:
        """Find ATM implied volatility"""
        if not strikes or not ivs:
            return None
        
        # Find closest strike to spot
        closest_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - spot_price))
        return ivs[closest_idx]
    
    def _calculate_skew_metrics(self, call_strikes: List[float], call_ivs: List[float],
                              put_strikes: List[float], put_ivs: List[float],
                              spot_price: float) -> Dict[str, Any]:
        """Calculate volatility skew metrics"""
        skew_metrics = {}
        
        # Calculate moneyness for all options
        call_moneyness = [strike / spot_price for strike in call_strikes]
        put_moneyness = [strike / spot_price for strike in put_strikes]
        
        # Combine all data
        all_moneyness = call_moneyness + put_moneyness
        all_ivs = call_ivs + put_ivs
        
        if len(all_moneyness) > 5:
            # Calculate skew slope (change in IV per unit moneyness)
            otm_puts = [(m, iv) for m, iv in zip(put_moneyness, put_ivs) if m < 0.95]
            otm_calls = [(m, iv) for m, iv in zip(call_moneyness, call_ivs) if m > 1.05]
            
            if otm_puts and otm_calls:
                put_iv_avg = np.mean([iv for _, iv in otm_puts])
                call_iv_avg = np.mean([iv for _, iv in otm_calls])
                skew_metrics["put_call_skew"] = put_iv_avg - call_iv_avg
            
            # Calculate smile curvature
            if len(all_moneyness) > 3:
                # Fit polynomial to IV curve
                try:
                    coeffs = np.polyfit(all_moneyness, all_ivs, 2)
                    skew_metrics["smile_curvature"] = coeffs[0]  # Second derivative
                except:
                    skew_metrics["smile_curvature"] = None
            
            # Risk reversal (25-delta put IV - 25-delta call IV)
            skew_metrics["risk_reversal"] = self._calculate_risk_reversal(
                call_strikes, call_ivs, put_strikes, put_ivs, spot_price
            )
        
        return skew_metrics
    
    def _calculate_risk_reversal(self, call_strikes: List[float], call_ivs: List[float],
                               put_strikes: List[float], put_ivs: List[float],
                               spot_price: float) -> Optional[float]:
        """Calculate 25-delta risk reversal"""
        # This is simplified - in practice you'd calculate exact 25-delta strikes
        # For now, use strikes approximately 25-delta equivalent
        
        # Approximate 25-delta strikes (roughly 10% OTM)
        target_call_strike = spot_price * 1.1
        target_put_strike = spot_price * 0.9
        
        # Find closest strikes
        call_iv = None
        put_iv = None
        
        if call_strikes and call_ivs:
            closest_call_idx = min(range(len(call_strikes)), 
                                 key=lambda i: abs(call_strikes[i] - target_call_strike))
            call_iv = call_ivs[closest_call_idx]
        
        if put_strikes and put_ivs:
            closest_put_idx = min(range(len(put_strikes)), 
                                key=lambda i: abs(put_strikes[i] - target_put_strike))
            put_iv = put_ivs[closest_put_idx]
        
        if call_iv and put_iv:
            return put_iv - call_iv
        
        return None
    
    def _analyze_term_structure(self, expiry_analysis: Dict, spot_price: float) -> Dict[str, Any]:
        """Analyze volatility term structure"""
        term_structure = {
            "expiries": [],
            "atm_ivs": [],
            "days_to_expiry": [],
            "term_structure_slope": None,
            "contango_backwardation": None
        }
        
        # Extract ATM IVs for each expiry
        for expiry_str, analysis in expiry_analysis.items():
            if analysis.get("atm_iv"):
                term_structure["expiries"].append(expiry_str)
                term_structure["atm_ivs"].append(analysis["atm_iv"])
                term_structure["days_to_expiry"].append(analysis["days_to_expiry"])
        
        if len(term_structure["atm_ivs"]) > 1:
            # Calculate term structure slope
            x = np.array(term_structure["days_to_expiry"])
            y = np.array(term_structure["atm_ivs"])
            
            if len(x) > 1:
                slope = np.polyfit(x, y, 1)[0]
                term_structure["term_structure_slope"] = slope
                
                # Determine if in contango or backwardation
                if slope > 0:
                    term_structure["contango_backwardation"] = "contango"  # IV increases with time
                else:
                    term_structure["contango_backwardation"] = "backwardation"  # IV decreases with time
        
        return term_structure

    def _analyze_volatility_skew_summary(self, expiry_analysis: Dict) -> Dict[str, Any]:
        """Analyze volatility skew across all expiries"""
        skew_summary = {
            "average_put_call_skew": 0.0,
            "average_risk_reversal": 0.0,
            "skew_consistency": 0.0,
            "expiry_count": len(expiry_analysis)
        }

        put_call_skews = []
        risk_reversals = []

        for expiry_str, analysis in expiry_analysis.items():
            skew_data = analysis.get("iv_skew", {})

            if "put_call_skew" in skew_data:
                put_call_skews.append(skew_data["put_call_skew"])

            if "risk_reversal" in skew_data:
                risk_reversals.append(skew_data["risk_reversal"])

        if put_call_skews:
            skew_summary["average_put_call_skew"] = np.mean(put_call_skews)
            skew_summary["skew_consistency"] = 1.0 - (np.std(put_call_skews) / max(0.01, abs(np.mean(put_call_skews))))

        if risk_reversals:
            skew_summary["average_risk_reversal"] = np.mean(risk_reversals)

        return skew_summary

    def _calculate_surface_metrics(self, expiry_analysis: Dict) -> Dict[str, Any]:
        """Calculate overall surface metrics"""
        all_ivs = []
        all_dte = []
        
        for analysis in expiry_analysis.values():
            if analysis.get("atm_iv"):
                all_ivs.append(analysis["atm_iv"])
                all_dte.append(analysis["days_to_expiry"])
        
        if not all_ivs:
            return {}
        
        metrics = {
            "average_iv": np.mean(all_ivs),
            "iv_range": max(all_ivs) - min(all_ivs),
            "iv_std": np.std(all_ivs),
            "short_term_iv": None,
            "long_term_iv": None,
            "iv_term_spread": None
        }
        
        # Separate short-term and long-term
        short_term_ivs = [iv for iv, dte in zip(all_ivs, all_dte) if dte <= 30]
        long_term_ivs = [iv for iv, dte in zip(all_ivs, all_dte) if dte > 30]
        
        if short_term_ivs:
            metrics["short_term_iv"] = np.mean(short_term_ivs)
        
        if long_term_ivs:
            metrics["long_term_iv"] = np.mean(long_term_ivs)
        
        if metrics["short_term_iv"] and metrics["long_term_iv"]:
            metrics["iv_term_spread"] = metrics["long_term_iv"] - metrics["short_term_iv"]
        
        return metrics
    
    def get_trading_signals_from_surface(self, surface_analysis: Dict, 
                                       vix_percentile_data: Dict) -> Dict[str, Any]:
        """
        Generate trading signals based on volatility surface analysis
        The Architect: This tells you when vol is actually cheap or expensive
        """
        signals = {
            "trade_recommendation": "HOLD",
            "confidence": 0.0,
            "reasoning": [],
            "surface_quality": "UNKNOWN",
            "vol_regime": "NORMAL"
        }
        
        surface_metrics = surface_analysis.get("surface_metrics", {})
        term_structure = surface_analysis.get("term_structure", {})
        
        # Check surface quality
        if len(surface_analysis.get("expiry_analysis", {})) < 2:
            signals["surface_quality"] = "POOR"
            signals["reasoning"].append("Insufficient expiry data for analysis")
            return signals
        
        signals["surface_quality"] = "GOOD"
        
        # Analyze term structure
        if term_structure.get("contango_backwardation") == "backwardation":
            signals["reasoning"].append("Volatility in backwardation - near-term vol expensive")
            signals["confidence"] += 0.2
        elif term_structure.get("contango_backwardation") == "contango":
            signals["reasoning"].append("Volatility in contango - far-term vol expensive")
            signals["confidence"] += 0.1
        
        # Check VIX percentile (The Architect's key filter)
        current_percentile = vix_percentile_data.get("current_percentile", 0.5)
        
        if current_percentile < 0.2:  # Bottom 20th percentile
            signals["trade_recommendation"] = "LONG_VOLATILITY"
            signals["vol_regime"] = "LOW_VOL"
            signals["confidence"] += 0.4
            signals["reasoning"].append(f"VIX in bottom 20th percentile ({current_percentile:.1%})")
        elif current_percentile > 0.8:  # Top 20th percentile
            signals["trade_recommendation"] = "SHORT_VOLATILITY"
            signals["vol_regime"] = "HIGH_VOL"
            signals["confidence"] += 0.4
            signals["reasoning"].append(f"VIX in top 20th percentile ({current_percentile:.1%})")
        
        # Check IV levels vs historical
        avg_iv = surface_metrics.get("average_iv", 0.15)
        if avg_iv < 0.12:  # Low IV environment
            signals["confidence"] += 0.2
            signals["reasoning"].append(f"Low IV environment ({avg_iv:.1%})")
        elif avg_iv > 0.25:  # High IV environment
            signals["confidence"] += 0.2
            signals["reasoning"].append(f"High IV environment ({avg_iv:.1%})")
        
        # Cap confidence at 1.0
        signals["confidence"] = min(1.0, signals["confidence"])
        
        return signals
