"""
Dynamic Position Sizing with <PERSON>
The Architect's implementation - size positions based on edge and volatility
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from scipy.optimize import minimize_scalar

logger = logging.getLogger(__name__)

@dataclass
class KellyParameters:
    """Kelly Criterion parameters for position sizing"""
    win_rate: float
    avg_win: float
    avg_loss: float
    kelly_fraction: float
    adjusted_fraction: float
    confidence: float
    sample_size: int

@dataclass
class PositionSizeResult:
    """Result of position sizing calculation"""
    base_lots: int
    kelly_lots: int
    volatility_adjusted_lots: int
    correlation_adjusted_lots: int
    final_lots: int
    size_factors: Dict[str, float]
    reasoning: str

class DynamicPositionSizing:
    """
    The Architect's Dynamic Position Sizing System
    
    Core Logic:
    - Kelly Criterion for optimal position sizing
    - Volatility adjustment: 50% reduction when VIX >25
    - Correlation adjustment for portfolio concentration
    - Minimum sample size requirements for reliability
    - Conservative fractional Kelly to avoid ruin
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size
        
        # Kelly parameters
        self.min_sample_size = 30  # Minimum trades for reliable <PERSON>
        self.max_kelly_fraction = 0.25  # Never risk more than 25% on Kelly
        self.fractional_kelly = 0.5  # Use 50% of Kelly (conservative)
        
        # Volatility thresholds
        self.vix_high_threshold = 25
        self.vix_extreme_threshold = 35
        
        # Correlation thresholds
        self.max_correlation = 0.7
        self.correlation_penalty_factor = 0.5
        
        # Base risk limits
        self.max_position_risk = 0.02  # 2% max per position
        self.max_portfolio_risk = 0.10  # 10% max total risk
        
        # Historical performance tracking
        self.trade_history: List[Dict] = []
        
    def calculate_kelly_fraction(self, trade_history: List[Dict]) -> KellyParameters:
        """
        Calculate Kelly fraction from historical trade performance
        The Architect: Let the data tell you how much to risk
        """
        if len(trade_history) < self.min_sample_size:
            return self._default_kelly_parameters(len(trade_history))
        
        # Extract P&L data
        pnl_data = [trade.get('pnl', 0) for trade in trade_history]
        
        # Separate wins and losses
        wins = [pnl for pnl in pnl_data if pnl > 0]
        losses = [abs(pnl) for pnl in pnl_data if pnl < 0]
        
        if not wins or not losses:
            return self._default_kelly_parameters(len(trade_history))
        
        # Calculate Kelly parameters
        win_rate = len(wins) / len(pnl_data)
        avg_win = np.mean(wins)
        avg_loss = np.mean(losses)
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        if avg_loss > 0:
            b = avg_win / avg_loss  # Reward to risk ratio
            kelly_fraction = (b * win_rate - (1 - win_rate)) / b
        else:
            kelly_fraction = 0
        
        # Cap Kelly fraction
        kelly_fraction = max(0, min(self.max_kelly_fraction, kelly_fraction))
        
        # Calculate confidence based on sample size and consistency
        confidence = self._calculate_kelly_confidence(pnl_data, win_rate, avg_win, avg_loss)
        
        return KellyParameters(
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            kelly_fraction=kelly_fraction,
            adjusted_fraction=kelly_fraction * self.fractional_kelly,
            confidence=confidence,
            sample_size=len(trade_history)
        )
    
    def calculate_position_size(self, signal: Dict[str, Any], 
                              current_positions: List[Dict],
                              current_vix: float,
                              correlation_matrix: Optional[np.ndarray] = None) -> PositionSizeResult:
        """
        Calculate optimal position size with all adjustments
        The Architect: Size positions to maximize long-term growth
        """
        # Base position size from risk management
        max_loss = signal.get('max_loss', 0)
        base_risk_amount = self.portfolio_size * self.max_position_risk
        
        if max_loss <= 0:
            max_loss = signal.get('total_premium', 100) * 75  # Fallback estimate
        
        base_lots = int(base_risk_amount / max_loss) if max_loss > 0 else 1
        base_lots = max(1, base_lots)
        
        # Kelly adjustment
        kelly_params = self.calculate_kelly_fraction(self.trade_history)
        kelly_multiplier = kelly_params.adjusted_fraction / self.max_position_risk
        kelly_lots = int(base_lots * kelly_multiplier)
        
        # Volatility adjustment
        vix_factor = self._calculate_volatility_factor(current_vix)
        volatility_adjusted_lots = int(kelly_lots * vix_factor)
        
        # Correlation adjustment
        correlation_factor = self._calculate_correlation_factor(
            signal, current_positions, correlation_matrix
        )
        correlation_adjusted_lots = int(volatility_adjusted_lots * correlation_factor)
        
        # Final position size (minimum 1 lot if any size calculated)
        final_lots = max(0, correlation_adjusted_lots)
        
        # Generate reasoning
        reasoning = self._generate_sizing_reasoning(
            kelly_params, vix_factor, correlation_factor, current_vix
        )
        
        size_factors = {
            "kelly_multiplier": kelly_multiplier,
            "volatility_factor": vix_factor,
            "correlation_factor": correlation_factor,
            "final_multiplier": (final_lots / base_lots) if base_lots > 0 else 0
        }
        
        return PositionSizeResult(
            base_lots=base_lots,
            kelly_lots=kelly_lots,
            volatility_adjusted_lots=volatility_adjusted_lots,
            correlation_adjusted_lots=correlation_adjusted_lots,
            final_lots=final_lots,
            size_factors=size_factors,
            reasoning=reasoning
        )
    
    def _calculate_volatility_factor(self, current_vix: float) -> float:
        """
        Calculate volatility adjustment factor
        The Architect: Reduce size when volatility spikes
        """
        if current_vix <= 20:
            return 1.2  # Increase size in low vol
        elif current_vix <= self.vix_high_threshold:
            return 1.0  # Normal size
        elif current_vix <= self.vix_extreme_threshold:
            return 0.5  # 50% reduction as specified
        else:
            return 0.25  # 75% reduction in extreme vol
    
    def _calculate_correlation_factor(self, signal: Dict[str, Any],
                                    current_positions: List[Dict],
                                    correlation_matrix: Optional[np.ndarray]) -> float:
        """
        Calculate correlation adjustment factor
        The Architect: Reduce size when positions are correlated
        """
        if not current_positions:
            return 1.0  # No correlation risk with no positions
        
        # Simple correlation estimate if matrix not provided
        if correlation_matrix is None:
            correlation_factor = self._estimate_strategy_correlation(signal, current_positions)
        else:
            correlation_factor = self._calculate_portfolio_correlation(
                signal, current_positions, correlation_matrix
            )
        
        # Apply correlation penalty
        if correlation_factor > self.max_correlation:
            penalty = 1 - ((correlation_factor - self.max_correlation) / (1 - self.max_correlation))
            return max(0.1, penalty * self.correlation_penalty_factor)
        
        return 1.0
    
    def _estimate_strategy_correlation(self, signal: Dict[str, Any], 
                                     current_positions: List[Dict]) -> float:
        """Estimate correlation based on strategy similarity"""
        signal_strategy = signal.get('strategy', '')
        signal_strike = signal.get('strike', 0)
        signal_expiry = signal.get('expiry_date', datetime.now())
        
        correlations = []
        
        for position in current_positions:
            pos_strategy = position.get('strategy', '')
            pos_strike = position.get('strike', 0)
            pos_expiry = position.get('expiry_date', datetime.now())
            
            # Strategy correlation
            strategy_corr = 0.8 if signal_strategy == pos_strategy else 0.3
            
            # Strike correlation (closer strikes = higher correlation)
            if signal_strike > 0 and pos_strike > 0:
                strike_diff_pct = abs(signal_strike - pos_strike) / signal_strike
                strike_corr = max(0, 1 - strike_diff_pct * 2)  # Linear decay
            else:
                strike_corr = 0.5
            
            # Expiry correlation
            if isinstance(signal_expiry, datetime) and isinstance(pos_expiry, datetime):
                days_diff = abs((signal_expiry - pos_expiry).days)
                expiry_corr = max(0, 1 - days_diff / 30)  # Decay over 30 days
            else:
                expiry_corr = 0.5
            
            # Combined correlation
            combined_corr = (strategy_corr + strike_corr + expiry_corr) / 3
            correlations.append(combined_corr)
        
        return np.mean(correlations) if correlations else 0
    
    def _calculate_portfolio_correlation(self, signal: Dict[str, Any],
                                       current_positions: List[Dict],
                                       correlation_matrix: np.ndarray) -> float:
        """Calculate portfolio correlation using correlation matrix"""
        # Simplified implementation - in practice would map positions to correlation matrix
        # For now, return average correlation
        n_positions = len(current_positions)
        if n_positions == 0:
            return 0
        
        # Extract upper triangle of correlation matrix (excluding diagonal)
        upper_triangle = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
        avg_correlation = np.mean(np.abs(upper_triangle))
        
        # Scale by number of positions
        portfolio_correlation = avg_correlation * min(1.0, n_positions / 5)
        
        return portfolio_correlation
    
    def _calculate_kelly_confidence(self, pnl_data: List[float], win_rate: float,
                                  avg_win: float, avg_loss: float) -> float:
        """Calculate confidence in Kelly estimate"""
        n = len(pnl_data)
        
        # Base confidence on sample size
        size_confidence = min(1.0, n / 100)  # Full confidence at 100+ trades
        
        # Confidence based on consistency
        pnl_std = np.std(pnl_data)
        avg_pnl = np.mean(pnl_data)
        
        if avg_pnl > 0 and pnl_std > 0:
            sharpe_like = avg_pnl / pnl_std
            consistency_confidence = min(1.0, sharpe_like / 2)  # Full confidence at Sharpe=2
        else:
            consistency_confidence = 0.1
        
        # Combined confidence
        return (size_confidence + consistency_confidence) / 2
    
    def _default_kelly_parameters(self, sample_size: int) -> KellyParameters:
        """Default Kelly parameters when insufficient data"""
        return KellyParameters(
            win_rate=0.6,  # Conservative assumption
            avg_win=100,
            avg_loss=100,
            kelly_fraction=0.1,  # Conservative 10%
            adjusted_fraction=0.05,  # Very conservative
            confidence=0.2,  # Low confidence
            sample_size=sample_size
        )
    
    def _generate_sizing_reasoning(self, kelly_params: KellyParameters,
                                 vix_factor: float, correlation_factor: float,
                                 current_vix: float) -> str:
        """Generate human-readable reasoning for position sizing"""
        reasons = []
        
        # Kelly reasoning
        if kelly_params.sample_size < self.min_sample_size:
            reasons.append(f"Limited history ({kelly_params.sample_size} trades) - conservative sizing")
        else:
            reasons.append(f"Kelly fraction {kelly_params.kelly_fraction:.2f} "
                         f"(WR: {kelly_params.win_rate:.1%}, confidence: {kelly_params.confidence:.1%})")
        
        # Volatility reasoning
        if vix_factor < 1.0:
            reasons.append(f"VIX {current_vix:.1f} - reduced size by {(1-vix_factor)*100:.0f}%")
        elif vix_factor > 1.0:
            reasons.append(f"Low VIX {current_vix:.1f} - increased size by {(vix_factor-1)*100:.0f}%")
        
        # Correlation reasoning
        if correlation_factor < 1.0:
            reasons.append(f"High correlation - reduced size by {(1-correlation_factor)*100:.0f}%")
        
        return "; ".join(reasons)
    
    def add_trade_result(self, trade_result: Dict[str, Any]):
        """Add trade result to history for Kelly calculation"""
        self.trade_history.append(trade_result)
        
        # Keep only recent history (last 200 trades)
        if len(self.trade_history) > 200:
            self.trade_history = self.trade_history[-200:]
    
    def get_sizing_statistics(self) -> Dict[str, Any]:
        """Get position sizing statistics"""
        kelly_params = self.calculate_kelly_fraction(self.trade_history)
        
        return {
            "kelly_parameters": {
                "win_rate": kelly_params.win_rate,
                "avg_win": kelly_params.avg_win,
                "avg_loss": kelly_params.avg_loss,
                "kelly_fraction": kelly_params.kelly_fraction,
                "adjusted_fraction": kelly_params.adjusted_fraction,
                "confidence": kelly_params.confidence,
                "sample_size": kelly_params.sample_size
            },
            "sizing_limits": {
                "max_kelly_fraction": self.max_kelly_fraction,
                "fractional_kelly": self.fractional_kelly,
                "max_position_risk": self.max_position_risk,
                "max_portfolio_risk": self.max_portfolio_risk
            },
            "thresholds": {
                "min_sample_size": self.min_sample_size,
                "vix_high_threshold": self.vix_high_threshold,
                "max_correlation": self.max_correlation
            },
            "trade_history_size": len(self.trade_history)
        }
    
    def validate_position_size(self, lots: int, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate if position size is within risk limits"""
        max_loss = signal.get('max_loss', 0)
        if max_loss <= 0:
            return True, "No max loss specified"
        
        total_risk = lots * max_loss
        max_allowed_risk = self.portfolio_size * self.max_position_risk
        
        if total_risk > max_allowed_risk:
            return False, f"Position risk ₹{total_risk:,.0f} exceeds limit ₹{max_allowed_risk:,.0f}"
        
        return True, "Position size validated"
