"""
Tail Risk Hedging System
The Architect's implementation - protect against unlimited loss scenarios
"""
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging

from models.options_greeks import OptionsGreeksCalculator
from models.data_models import OptionsData

logger = logging.getLogger(__name__)

class TailRiskHedgingSystem:
    """
    Tail risk hedging for short volatility positions
    The Architect: This is what keeps you from blowing up during black swan events
    """
    
    def __init__(self, hedge_budget_pct: float = 0.15):
        self.hedge_budget_pct = hedge_budget_pct  # 15% of premium for hedging
        self.greeks_calculator = OptionsGreeksCalculator()
        
        # Hedging parameters
        self.min_hedge_delta = 0.05  # Minimum 5 delta for hedge options
        self.max_hedge_delta = 0.20  # Maximum 20 delta for hedge options
        self.hedge_expiry_buffer = 7  # Hedge should expire at least 7 days after main position
        
    def design_tail_hedge(self, short_vol_position: Dict[str, Any], 
                         available_options: List[OptionsData],
                         spot_price: float) -> Dict[str, Any]:
        """
        Design tail risk hedge for a short volatility position
        The Architect: This defines your maximum loss before you blow up
        """
        hedge_design = {
            "hedge_required": False,
            "hedge_positions": [],
            "hedge_cost": 0.0,
            "max_loss_without_hedge": float('inf'),
            "max_loss_with_hedge": 0.0,
            "hedge_effectiveness": 0.0,
            "reasoning": []
        }
        
        # Only hedge short volatility positions
        if short_vol_position.get("strategy") != "SHORT_VOLATILITY":
            hedge_design["reasoning"].append("No hedge needed for long volatility positions")
            return hedge_design
        
        hedge_design["hedge_required"] = True
        
        # Calculate premium collected from short position
        premium_collected = short_vol_position.get("entry_premium", 0)
        hedge_budget = premium_collected * self.hedge_budget_pct
        
        # Find suitable hedge options (OTM puts)
        hedge_candidates = self._find_hedge_candidates(
            available_options, short_vol_position, spot_price
        )
        
        if not hedge_candidates:
            hedge_design["reasoning"].append("No suitable hedge options found")
            return hedge_design
        
        # Select optimal hedge
        optimal_hedge = self._select_optimal_hedge(
            hedge_candidates, hedge_budget, short_vol_position, spot_price
        )
        
        if optimal_hedge:
            hedge_design["hedge_positions"] = [optimal_hedge]
            hedge_design["hedge_cost"] = optimal_hedge["cost"]
            
            # Calculate maximum loss with hedge
            hedge_design["max_loss_with_hedge"] = self._calculate_max_loss_with_hedge(
                short_vol_position, optimal_hedge, spot_price
            )
            
            # Calculate hedge effectiveness
            max_loss_without = premium_collected * 3  # Rough estimate
            hedge_design["max_loss_without_hedge"] = max_loss_without
            
            if max_loss_without > 0:
                hedge_design["hedge_effectiveness"] = (
                    (max_loss_without - hedge_design["max_loss_with_hedge"]) / max_loss_without
                )
            
            hedge_design["reasoning"].append(
                f"Hedge reduces max loss from ₹{max_loss_without:.0f} to ₹{hedge_design['max_loss_with_hedge']:.0f}"
            )
        else:
            hedge_design["reasoning"].append("No cost-effective hedge found within budget")
        
        return hedge_design
    
    def _find_hedge_candidates(self, available_options: List[OptionsData],
                             short_vol_position: Dict[str, Any],
                             spot_price: float) -> List[Dict[str, Any]]:
        """Find suitable options for tail risk hedging"""
        candidates = []
        
        # Get main position expiry
        main_expiry = None
        for leg in short_vol_position.get("legs", []):
            if main_expiry is None or leg["expiry"] > main_expiry:
                main_expiry = leg["expiry"]
        
        if not main_expiry:
            return candidates
        
        # Convert to date if needed
        if isinstance(main_expiry, str):
            main_expiry = datetime.strptime(main_expiry, "%Y-%m-%d").date()
        elif isinstance(main_expiry, datetime):
            main_expiry = main_expiry.date()
        
        for option in available_options:
            # Only consider puts for downside protection
            if option.option_type != "PE":
                continue
            
            # Check expiry (should be same or later than main position)
            option_expiry = option.expiry_date
            if isinstance(option_expiry, datetime):
                option_expiry = option_expiry.date()
            
            if option_expiry < main_expiry:
                continue
            
            # Check if OTM put
            if option.strike >= spot_price * 0.95:  # Not sufficiently OTM
                continue
            
            # Check liquidity
            if option.open_interest < 100 or option.volume < 10:
                continue
            
            # Calculate delta to ensure it's in our range
            dte = (option_expiry - datetime.now().date()).days
            time_to_expiry = dte / 365.0
            
            if time_to_expiry > 0:
                delta = abs(self.greeks_calculator.calculate_delta(
                    spot_price, option.strike, time_to_expiry, 
                    option.implied_volatility / 100 if option.implied_volatility > 1 else 0.15,
                    "PE"
                ))
                
                if self.min_hedge_delta <= delta <= self.max_hedge_delta:
                    candidates.append({
                        "option": option,
                        "delta": delta,
                        "cost_per_lot": option.last_price * 75,  # NSE lot size
                        "strike": option.strike,
                        "expiry": option_expiry,
                        "dte": dte
                    })
        
        # Sort by cost-effectiveness (delta per rupee)
        candidates.sort(key=lambda x: x["delta"] / x["cost_per_lot"], reverse=True)
        
        return candidates
    
    def _select_optimal_hedge(self, candidates: List[Dict[str, Any]], 
                            hedge_budget: float,
                            short_vol_position: Dict[str, Any],
                            spot_price: float) -> Optional[Dict[str, Any]]:
        """Select the most cost-effective hedge within budget"""
        
        lots_short = short_vol_position.get("lots", 1)
        
        for candidate in candidates:
            cost_per_lot = candidate["cost_per_lot"]
            
            # Calculate how many hedge lots we can afford
            max_hedge_lots = int(hedge_budget / cost_per_lot)
            
            if max_hedge_lots < 1:
                continue
            
            # For short straddle, hedge with same number of lots
            # For more sophisticated strategies, adjust ratio
            hedge_lots = min(max_hedge_lots, lots_short)
            
            total_cost = hedge_lots * cost_per_lot
            
            if total_cost <= hedge_budget:
                return {
                    "option": candidate["option"],
                    "lots": hedge_lots,
                    "cost": total_cost,
                    "delta": candidate["delta"],
                    "strike": candidate["strike"],
                    "expiry": candidate["expiry"],
                    "hedge_ratio": hedge_lots / lots_short
                }
        
        return None
    
    def _calculate_max_loss_with_hedge(self, short_vol_position: Dict[str, Any],
                                     hedge: Dict[str, Any],
                                     spot_price: float) -> float:
        """Calculate maximum loss with hedge in place"""
        
        # This is a simplified calculation
        # In practice, you'd run Monte Carlo scenarios
        
        premium_collected = short_vol_position.get("entry_premium", 0)
        hedge_cost = hedge["cost"]
        hedge_strike = hedge["strike"]
        
        # Worst case: market crashes to hedge strike
        # Short straddle loss at hedge strike
        main_strike = short_vol_position.get("legs", [{}])[0].get("strike", spot_price)
        
        # Simplified P&L calculation at hedge strike
        straddle_loss_at_hedge_strike = abs(hedge_strike - main_strike) * 75  # Per lot
        straddle_loss_total = straddle_loss_at_hedge_strike * short_vol_position.get("lots", 1)
        
        # Hedge profit at hedge strike (intrinsic value)
        hedge_profit_per_lot = max(0, main_strike - hedge_strike) * 75
        hedge_profit_total = hedge_profit_per_lot * hedge.get("lots", 1)
        
        # Net loss = straddle loss - hedge profit + hedge cost - premium collected
        net_loss = straddle_loss_total - hedge_profit_total + hedge_cost - premium_collected
        
        return max(0, net_loss)
    
    def monitor_hedge_effectiveness(self, position_with_hedge: Dict[str, Any],
                                  current_spot: float,
                                  current_vix: float) -> Dict[str, Any]:
        """
        Monitor hedge effectiveness in real-time
        The Architect: Know if your hedge is working before you need it
        """
        monitoring = {
            "hedge_status": "ACTIVE",
            "effectiveness_score": 0.0,
            "recommendations": [],
            "risk_metrics": {}
        }
        
        # Check if hedge is still valid
        hedge_positions = position_with_hedge.get("hedge_positions", [])
        
        if not hedge_positions:
            monitoring["hedge_status"] = "NO_HEDGE"
            monitoring["recommendations"].append("Consider adding tail risk hedge")
            return monitoring
        
        hedge = hedge_positions[0]
        
        # Check time decay on hedge
        hedge_expiry = hedge.get("expiry")
        if isinstance(hedge_expiry, str):
            hedge_expiry = datetime.strptime(hedge_expiry, "%Y-%m-%d").date()
        
        days_to_hedge_expiry = (hedge_expiry - datetime.now().date()).days
        
        if days_to_hedge_expiry < 7:
            monitoring["recommendations"].append("Hedge expiring soon - consider rolling")
        
        # Check if hedge is moving into the money
        hedge_strike = hedge.get("strike", 0)
        distance_to_hedge = (current_spot - hedge_strike) / current_spot
        
        if distance_to_hedge < 0.1:  # Within 10% of hedge strike
            monitoring["recommendations"].append("Approaching hedge strike - monitor closely")
            monitoring["effectiveness_score"] += 0.3
        
        # Check VIX level for hedge adjustment
        if current_vix > 30:
            monitoring["recommendations"].append("High VIX - hedge becoming more valuable")
            monitoring["effectiveness_score"] += 0.4
        elif current_vix < 12:
            monitoring["recommendations"].append("Low VIX - consider reducing hedge cost")
        
        # Calculate current hedge value (simplified)
        hedge_intrinsic = max(0, hedge_strike - current_spot)
        monitoring["risk_metrics"]["hedge_intrinsic_value"] = hedge_intrinsic
        monitoring["risk_metrics"]["distance_to_hedge_pct"] = distance_to_hedge * 100
        monitoring["risk_metrics"]["days_to_hedge_expiry"] = days_to_hedge_expiry
        
        return monitoring
    
    def calculate_dynamic_hedge_ratio(self, portfolio_delta: float, 
                                    portfolio_gamma: float,
                                    current_vix: float) -> float:
        """
        Calculate dynamic hedge ratio based on portfolio Greeks
        The Architect: Adjust hedge size based on actual risk exposure
        """
        base_ratio = 1.0  # 1:1 hedge ratio
        
        # Adjust for portfolio delta
        if abs(portfolio_delta) > 0.1:
            delta_adjustment = min(0.5, abs(portfolio_delta) / 0.2)
            base_ratio += delta_adjustment
        
        # Adjust for gamma risk
        if abs(portfolio_gamma) > 0.02:
            gamma_adjustment = min(0.3, abs(portfolio_gamma) / 0.05)
            base_ratio += gamma_adjustment
        
        # Adjust for VIX level
        if current_vix > 25:
            vix_adjustment = min(0.4, (current_vix - 25) / 25)
            base_ratio += vix_adjustment
        
        return min(2.0, base_ratio)  # Cap at 2:1 ratio
