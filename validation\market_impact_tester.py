"""
Market Impact Tester
The Architect's Fix #5: Test Market Impact

Before going live, simulate your strategy with realistic position sizes.
If you're trading ₹10 lakh positions in options with ₹50 lakh daily volume,
you ARE the market. Model this impact or reduce size until you're not.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ImpactSeverity(Enum):
    """Market impact severity levels"""
    NEGLIGIBLE = "negligible"  # <1% of daily volume
    LOW = "low"               # 1-5% of daily volume
    MODERATE = "moderate"     # 5-15% of daily volume
    HIGH = "high"            # 15-30% of daily volume
    EXTREME = "extreme"      # >30% of daily volume

@dataclass
class MarketImpactScenario:
    """Market impact test scenario"""
    position_size_lakhs: float
    daily_volume_lakhs: float
    option_price: float
    bid_ask_spread_bps: float
    time_to_expiry_days: int
    volatility_percentile: float

@dataclass
class ImpactTestResult:
    """Results of market impact test"""
    scenario: MarketImpactScenario
    volume_percentage: float
    impact_severity: ImpactSeverity
    estimated_slippage_bps: float
    price_impact_bps: float
    execution_probability: float
    recommended_max_size_lakhs: float
    is_tradeable: bool
    warnings: List[str]

class MarketImpactTester:
    """
    Tests market impact of trading strategies
    Brutal reality: if you're >5% of daily volume, you ARE the market
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # Impact thresholds
        self.negligible_threshold = self.config.get('negligible_threshold', 0.01)  # 1%
        self.low_threshold = self.config.get('low_threshold', 0.05)               # 5%
        self.moderate_threshold = self.config.get('moderate_threshold', 0.15)     # 15%
        self.high_threshold = self.config.get('high_threshold', 0.30)            # 30%

        # Impact coefficients (how much slippage per % of volume)
        self.impact_coefficients = {
            ImpactSeverity.NEGLIGIBLE: 0.5,  # 0.5 bps per 1% volume
            ImpactSeverity.LOW: 2.0,         # 2 bps per 1% volume
            ImpactSeverity.MODERATE: 8.0,    # 8 bps per 1% volume
            ImpactSeverity.HIGH: 25.0,       # 25 bps per 1% volume
            ImpactSeverity.EXTREME: 100.0    # 100 bps per 1% volume
        }

        # Execution probability factors
        self.base_execution_prob = self.config.get('base_execution_prob', 0.95)

    def test_market_impact(self, scenario: MarketImpactScenario) -> ImpactTestResult:
        """
        Test market impact for a given scenario

        Args:
            scenario: Market impact scenario to test

        Returns:
            Impact test results with brutal honesty
        """
        # Calculate volume percentage
        volume_percentage = scenario.position_size_lakhs / scenario.daily_volume_lakhs

        # Determine impact severity
        impact_severity = self._classify_impact_severity(volume_percentage)

        # Calculate estimated slippage
        estimated_slippage = self._calculate_slippage(scenario, volume_percentage, impact_severity)

        # Calculate price impact
        price_impact = self._calculate_price_impact(scenario, volume_percentage, impact_severity)

        # Calculate execution probability
        execution_prob = self._calculate_execution_probability(scenario, volume_percentage)

        # Determine recommended maximum size
        recommended_max_size = self._calculate_recommended_max_size(scenario)

        # Check if tradeable
        is_tradeable = self._is_tradeable(volume_percentage, estimated_slippage, execution_prob)

        # Generate warnings
        warnings = self._generate_warnings(scenario, volume_percentage, impact_severity)

        return ImpactTestResult(
            scenario=scenario,
            volume_percentage=volume_percentage,
            impact_severity=impact_severity,
            estimated_slippage_bps=estimated_slippage,
            price_impact_bps=price_impact,
            execution_probability=execution_prob,
            recommended_max_size_lakhs=recommended_max_size,
            is_tradeable=is_tradeable,
            warnings=warnings
        )

    def _classify_impact_severity(self, volume_percentage: float) -> ImpactSeverity:
        """
        Classify impact severity based on volume percentage

        Args:
            volume_percentage: Position size as percentage of daily volume

        Returns:
            Impact severity classification
        """
        if volume_percentage <= self.negligible_threshold:
            return ImpactSeverity.NEGLIGIBLE
        elif volume_percentage <= self.low_threshold:
            return ImpactSeverity.LOW
        elif volume_percentage <= self.moderate_threshold:
            return ImpactSeverity.MODERATE
        elif volume_percentage <= self.high_threshold:
            return ImpactSeverity.HIGH
        else:
            return ImpactSeverity.EXTREME

    def _calculate_slippage(
        self,
        scenario: MarketImpactScenario,
        volume_percentage: float,
        impact_severity: ImpactSeverity
    ) -> float:
        """
        Calculate estimated slippage in basis points

        Args:
            scenario: Market scenario
            volume_percentage: Volume percentage
            impact_severity: Impact severity

        Returns:
            Estimated slippage in basis points
        """
        # Base slippage from bid-ask spread
        base_slippage = scenario.bid_ask_spread_bps / 2

        # Market impact slippage
        impact_coefficient = self.impact_coefficients[impact_severity]
        impact_slippage = volume_percentage * 100 * impact_coefficient

        # Time to expiry factor (less time = more slippage)
        time_factor = max(0.5, 1.0 - (7 - scenario.time_to_expiry_days) / 7)

        # Volatility factor (higher volatility = more slippage)
        vol_factor = 1.0 + (scenario.volatility_percentile - 50) / 100

        total_slippage = (base_slippage + impact_slippage) * time_factor * vol_factor

        return total_slippage

    def _calculate_price_impact(
        self,
        scenario: MarketImpactScenario,
        volume_percentage: float,
        impact_severity: ImpactSeverity
    ) -> float:
        """
        Calculate permanent price impact in basis points

        Args:
            scenario: Market scenario
            volume_percentage: Volume percentage
            impact_severity: Impact severity

        Returns:
            Price impact in basis points
        """
        # Price impact is typically 30-50% of slippage
        slippage = self._calculate_slippage(scenario, volume_percentage, impact_severity)
        price_impact = slippage * 0.4  # 40% of slippage becomes permanent

        return price_impact

    def _calculate_execution_probability(
        self,
        scenario: MarketImpactScenario,
        volume_percentage: float
    ) -> float:
        """
        Calculate probability of successful execution

        Args:
            scenario: Market scenario
            volume_percentage: Volume percentage

        Returns:
            Execution probability (0-1)
        """
        # Base probability
        prob = self.base_execution_prob

        # Reduce probability based on volume percentage
        if volume_percentage > 0.05:  # >5%
            prob *= 0.8
        if volume_percentage > 0.15:  # >15%
            prob *= 0.6
        if volume_percentage > 0.30:  # >30%
            prob *= 0.3

        # Reduce probability for near expiry
        if scenario.time_to_expiry_days <= 1:
            prob *= 0.7

        # Reduce probability for high volatility
        if scenario.volatility_percentile > 80:
            prob *= 0.8

        return max(prob, 0.1)  # Minimum 10% chance

    def _calculate_recommended_max_size(self, scenario: MarketImpactScenario) -> float:
        """
        Calculate recommended maximum position size

        Args:
            scenario: Market scenario

        Returns:
            Recommended max size in lakhs
        """
        # Target 5% of daily volume as maximum
        target_percentage = 0.05
        recommended_size = scenario.daily_volume_lakhs * target_percentage

        return recommended_size

    def _is_tradeable(
        self,
        volume_percentage: float,
        estimated_slippage: float,
        execution_prob: float
    ) -> bool:
        """
        Determine if position is tradeable

        Args:
            volume_percentage: Volume percentage
            estimated_slippage: Estimated slippage
            execution_prob: Execution probability

        Returns:
            True if tradeable
        """
        # Not tradeable if too large relative to volume
        if volume_percentage > 0.15:  # >15%
            return False

        # Not tradeable if slippage too high
        if estimated_slippage > 1000:  # >10%
            return False

        # Not tradeable if execution probability too low
        if execution_prob < 0.5:  # <50%
            return False

        return True

    def _generate_warnings(
        self,
        scenario: MarketImpactScenario,
        volume_percentage: float,
        impact_severity: ImpactSeverity
    ) -> List[str]:
        """
        Generate warnings about market impact

        Args:
            scenario: Market scenario
            volume_percentage: Volume percentage
            impact_severity: Impact severity

        Returns:
            List of warnings
        """
        warnings = []

        if volume_percentage > 0.20:
            warnings.append(f"EXTREME RISK: Position is {volume_percentage:.1%} of daily volume - YOU ARE THE MARKET")

        if volume_percentage > 0.10:
            warnings.append(f"HIGH IMPACT: Position is {volume_percentage:.1%} of daily volume - expect significant slippage")

        if impact_severity == ImpactSeverity.EXTREME:
            warnings.append("EXTREME MARKET IMPACT: Consider reducing position size by 80%+")

        if scenario.time_to_expiry_days <= 1:
            warnings.append("EXPIRY RISK: <1 day to expiry increases execution difficulty")

        if scenario.volatility_percentile > 90:
            warnings.append("HIGH VOLATILITY: Extreme volatility increases all execution costs")

        if scenario.bid_ask_spread_bps > 500:
            warnings.append("WIDE SPREADS: >5% bid-ask spread indicates poor liquidity")

        return warnings