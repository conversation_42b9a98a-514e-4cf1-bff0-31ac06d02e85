"""
Realistic NSE Execution Simulation
The Architect's implementation - no more fantasy execution, real market conditions
"""
import random
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ExecutionStatus(Enum):
    FULL_FILL = "FULL_FILL"
    PARTIAL_FILL = "PARTIAL_FILL"
    REJECTED = "REJECTED"
    PENDING = "PENDING"

@dataclass
class ExecutionResult:
    """Result of order execution attempt"""
    order_id: str
    status: ExecutionStatus
    requested_quantity: int
    filled_quantity: int
    avg_fill_price: float
    total_cost: float
    execution_time_ms: int
    slippage: float
    impact_cost: float
    spread_cost: float
    rejection_reason: Optional[str]
    timestamp: datetime

class RealisticNSEExecution:
    """
    The Architect's Realistic NSE Execution Engine
    
    Real Market Conditions:
    - 15-50bp spreads based on liquidity
    - 70% full fills, 20% partial fills, 10% rejections
    - Impact costs scaling with order size vs ADV
    - Execution delays: 50-1000ms realistic timing
    - Market stress adjustments
    """
    
    def __init__(self):
        # Execution probabilities (base rates)
        self.full_fill_prob = 0.70
        self.partial_fill_prob = 0.20
        self.rejection_prob = 0.10
        
        # Spread parameters (basis points)
        self.min_spread_bp = 15
        self.max_spread_bp = 50
        self.base_spread_bp = 25
        
        # Execution timing (milliseconds)
        self.min_execution_time = 50
        self.max_execution_time = 1000
        self.avg_execution_time = 200
        
        # Market impact parameters
        self.impact_coefficient = 0.1  # 10bp per 1% of ADV
        self.liquidity_decay_factor = 1.5  # Non-linear impact
        
        # NSE specific parameters
        self.nse_lot_size = 75
        self.tick_size = 0.05
        self.max_order_value = 10000000  # ₹1 crore max
        
        # Market stress indicators
        self.current_vix = 16.0
        self.market_stress_factor = 1.0
        
    def execute_order(self, order: Dict[str, Any], market_data: Dict[str, Any]) -> ExecutionResult:
        """
        Execute order with realistic NSE conditions
        The Architect: This is how orders actually get filled
        """
        order_id = order.get('order_id', f"ORD_{datetime.now().strftime('%H%M%S')}")
        symbol = order.get('symbol', 'NIFTY')
        strike = order.get('strike', 19500)
        option_type = order.get('option_type', 'CE')
        quantity = order.get('quantity', 75)
        order_type = order.get('order_type', 'MARKET')
        
        # Get market data
        bid_price = market_data.get('bid_price', 100)
        ask_price = market_data.get('ask_price', 105)
        last_price = market_data.get('last_price', 102.5)
        volume = market_data.get('volume', 1000)
        open_interest = market_data.get('open_interest', 5000)
        
        # Calculate execution parameters
        spread_bp = self._calculate_dynamic_spread(bid_price, ask_price, volume, open_interest)
        execution_probs = self._calculate_execution_probabilities(quantity, volume, market_data)
        execution_time = self._calculate_execution_time(quantity, volume)
        
        # Determine execution outcome
        execution_status = self._determine_execution_status(execution_probs)
        
        if execution_status == ExecutionStatus.REJECTED:
            return self._create_rejection_result(order_id, quantity, execution_time)
        
        # Calculate fill details
        filled_quantity, avg_fill_price = self._calculate_fill_details(
            execution_status, quantity, bid_price, ask_price, last_price, spread_bp
        )
        
        # Calculate costs
        total_cost = filled_quantity * avg_fill_price
        slippage = self._calculate_slippage(avg_fill_price, last_price, order_type)
        impact_cost = self._calculate_impact_cost(filled_quantity, volume, avg_fill_price)
        spread_cost = self._calculate_spread_cost(filled_quantity, spread_bp, avg_fill_price)
        
        return ExecutionResult(
            order_id=order_id,
            status=execution_status,
            requested_quantity=quantity,
            filled_quantity=filled_quantity,
            avg_fill_price=avg_fill_price,
            total_cost=total_cost,
            execution_time_ms=execution_time,
            slippage=slippage,
            impact_cost=impact_cost,
            spread_cost=spread_cost,
            rejection_reason=None,
            timestamp=datetime.now()
        )
    
    def _calculate_dynamic_spread(self, bid: float, ask: float, volume: int, oi: int) -> float:
        """Calculate dynamic spread based on market conditions"""
        # Base spread from market data
        quoted_spread_bp = ((ask - bid) / ((ask + bid) / 2)) * 10000 if ask > bid else self.base_spread_bp
        
        # Liquidity adjustments
        volume_factor = max(0.5, min(2.0, 1000 / max(volume, 100)))  # Higher spread for low volume
        oi_factor = max(0.5, min(2.0, 5000 / max(oi, 500)))  # Higher spread for low OI
        
        # VIX adjustment
        vix_factor = 1.0 + (max(0, self.current_vix - 15) / 15) * 0.5  # +50% spread when VIX=30
        
        # Market stress adjustment
        stress_factor = self.market_stress_factor
        
        # Combined spread
        effective_spread_bp = quoted_spread_bp * volume_factor * oi_factor * vix_factor * stress_factor
        
        return max(self.min_spread_bp, min(self.max_spread_bp, effective_spread_bp))
    
    def _calculate_execution_probabilities(self, quantity: int, volume: int, 
                                         market_data: Dict) -> Dict[str, float]:
        """Calculate execution probabilities based on order size and liquidity"""
        # Base probabilities
        full_fill = self.full_fill_prob
        partial_fill = self.partial_fill_prob
        rejection = self.rejection_prob
        
        # Size vs volume adjustment
        order_size_ratio = quantity / max(volume, 100)
        
        if order_size_ratio > 0.5:  # Large order
            full_fill *= 0.3  # Much lower chance of full fill
            partial_fill *= 2.0  # Higher chance of partial
            rejection *= 3.0  # Much higher rejection
        elif order_size_ratio > 0.2:  # Medium order
            full_fill *= 0.7
            partial_fill *= 1.3
            rejection *= 1.5
        
        # VIX adjustment (higher vol = more rejections)
        if self.current_vix > 25:
            full_fill *= 0.6
            partial_fill *= 1.2
            rejection *= 2.0
        elif self.current_vix > 20:
            full_fill *= 0.8
            partial_fill *= 1.1
            rejection *= 1.3
        
        # Normalize probabilities
        total = full_fill + partial_fill + rejection
        return {
            'full_fill': full_fill / total,
            'partial_fill': partial_fill / total,
            'rejection': rejection / total
        }
    
    def _calculate_execution_time(self, quantity: int, volume: int) -> int:
        """Calculate realistic execution time"""
        # Base time
        base_time = self.avg_execution_time
        
        # Size adjustment
        size_factor = 1 + (quantity / (volume + 1)) * 2  # Larger orders take longer
        
        # VIX adjustment (higher vol = slower execution)
        vix_factor = 1 + max(0, (self.current_vix - 15) / 15) * 0.5
        
        # Random variation
        random_factor = random.uniform(0.5, 1.5)
        
        execution_time = int(base_time * size_factor * vix_factor * random_factor)
        
        return max(self.min_execution_time, min(self.max_execution_time, execution_time))
    
    def _determine_execution_status(self, probs: Dict[str, float]) -> ExecutionStatus:
        """Determine execution outcome based on probabilities"""
        rand = random.random()
        
        if rand < probs['rejection']:
            return ExecutionStatus.REJECTED
        elif rand < probs['rejection'] + probs['partial_fill']:
            return ExecutionStatus.PARTIAL_FILL
        else:
            return ExecutionStatus.FULL_FILL
    
    def _calculate_fill_details(self, status: ExecutionStatus, quantity: int,
                              bid: float, ask: float, last: float, spread_bp: float) -> Tuple[int, float]:
        """Calculate filled quantity and average price"""
        if status == ExecutionStatus.REJECTED:
            return 0, 0.0
        
        # Filled quantity
        if status == ExecutionStatus.FULL_FILL:
            filled_qty = quantity
        else:  # PARTIAL_FILL
            # Random partial fill between 20-80% of order
            fill_ratio = random.uniform(0.2, 0.8)
            filled_qty = max(1, int(quantity * fill_ratio))
        
        # Average fill price (market orders get worse prices)
        mid_price = (bid + ask) / 2
        
        # Price slippage based on spread and market impact
        slippage_factor = random.uniform(0.3, 0.7)  # Get 30-70% of spread as slippage
        price_slippage = (spread_bp / 10000) * mid_price * slippage_factor
        
        # Assume buying (add slippage), for selling subtract slippage
        avg_fill_price = mid_price + price_slippage
        
        # Round to tick size
        avg_fill_price = round(avg_fill_price / self.tick_size) * self.tick_size
        
        return filled_qty, max(self.tick_size, avg_fill_price)
    
    def _calculate_slippage(self, fill_price: float, reference_price: float, order_type: str) -> float:
        """Calculate slippage vs reference price"""
        if reference_price <= 0:
            return 0.0
        
        return ((fill_price - reference_price) / reference_price) * 100  # Percentage
    
    def _calculate_impact_cost(self, quantity: int, volume: int, price: float) -> float:
        """Calculate market impact cost"""
        if volume <= 0:
            return price * 0.05  # 5% impact if no volume data
        
        # Impact based on order size vs average daily volume
        impact_ratio = quantity / volume
        
        # Non-linear impact (square root function)
        impact_pct = self.impact_coefficient * (impact_ratio ** self.liquidity_decay_factor)
        
        return price * impact_pct
    
    def _calculate_spread_cost(self, quantity: int, spread_bp: float, price: float) -> float:
        """Calculate spread cost"""
        spread_cost_per_unit = (spread_bp / 10000) * price * 0.5  # Half spread
        return spread_cost_per_unit * quantity
    
    def _create_rejection_result(self, order_id: str, quantity: int, execution_time: int) -> ExecutionResult:
        """Create rejection result with realistic reasons"""
        rejection_reasons = [
            "Insufficient liquidity",
            "Price limit exceeded",
            "Market volatility too high",
            "Order size too large",
            "System busy - try again",
            "Risk management rejection"
        ]
        
        # Weight reasons based on market conditions
        if self.current_vix > 25:
            reason = random.choice(["Market volatility too high", "Risk management rejection"])
        else:
            reason = random.choice(rejection_reasons)
        
        return ExecutionResult(
            order_id=order_id,
            status=ExecutionStatus.REJECTED,
            requested_quantity=quantity,
            filled_quantity=0,
            avg_fill_price=0.0,
            total_cost=0.0,
            execution_time_ms=execution_time,
            slippage=0.0,
            impact_cost=0.0,
            spread_cost=0.0,
            rejection_reason=reason,
            timestamp=datetime.now()
        )
    
    def update_market_conditions(self, vix: float, stress_factor: float = 1.0):
        """Update market conditions for execution simulation"""
        self.current_vix = vix
        self.market_stress_factor = stress_factor
        
        logger.info(f"Updated execution conditions: VIX={vix:.1f}, Stress={stress_factor:.2f}")
    
    def get_execution_statistics(self, results: List[ExecutionResult]) -> Dict[str, Any]:
        """Calculate execution statistics from results"""
        if not results:
            return {"error": "No execution results"}
        
        total_orders = len(results)
        full_fills = sum(1 for r in results if r.status == ExecutionStatus.FULL_FILL)
        partial_fills = sum(1 for r in results if r.status == ExecutionStatus.PARTIAL_FILL)
        rejections = sum(1 for r in results if r.status == ExecutionStatus.REJECTED)
        
        successful_orders = [r for r in results if r.filled_quantity > 0]
        
        if successful_orders:
            avg_slippage = np.mean([r.slippage for r in successful_orders])
            avg_execution_time = np.mean([r.execution_time_ms for r in successful_orders])
            total_impact_cost = sum(r.impact_cost for r in successful_orders)
            total_spread_cost = sum(r.spread_cost for r in successful_orders)
        else:
            avg_slippage = 0
            avg_execution_time = 0
            total_impact_cost = 0
            total_spread_cost = 0
        
        return {
            "total_orders": total_orders,
            "execution_rates": {
                "full_fill": full_fills / total_orders,
                "partial_fill": partial_fills / total_orders,
                "rejection": rejections / total_orders
            },
            "performance": {
                "avg_slippage_pct": avg_slippage,
                "avg_execution_time_ms": avg_execution_time,
                "total_impact_cost": total_impact_cost,
                "total_spread_cost": total_spread_cost
            },
            "market_conditions": {
                "vix": self.current_vix,
                "stress_factor": self.market_stress_factor
            }
        }
